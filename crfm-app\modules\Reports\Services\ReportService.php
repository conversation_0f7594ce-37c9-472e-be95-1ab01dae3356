<?php

namespace Modules\Reports\Services;

use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;
use Modules\Pensions\Models\DossierPension;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class ReportService
{
    /**
     * Generate adherents report
     */
    public function generateAdherentsReport(array $filters = []): array
    {
        $query = Adherent::query();

        // Apply filters
        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['employeur'])) {
            $query->where('employeur', 'LIKE', "%{$filters['employeur']}%");
        }

        if (!empty($filters['date_from'])) {
            $query->where('date_adhesion', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date_adhesion', '<=', $filters['date_to']);
        }

        $adherents = $query->with(['cotisations', 'dossierPension'])->get();

        // Statistics
        $statistics = [
            'total_adherents' => $adherents->count(),
            'adherents_actifs' => $adherents->where('statut', Adherent::STATUS_ACTIF)->count(),
            'adherents_suspendus' => $adherents->where('statut', Adherent::STATUS_SUSPENDU)->count(),
            'adherents_radies' => $adherents->where('statut', Adherent::STATUS_RADIE)->count(),
            'adherents_retraites' => $adherents->where('statut', Adherent::STATUS_RETRAITE)->count(),
            'hommes' => $adherents->where('sexe', Adherent::SEXE_MASCULIN)->count(),
            'femmes' => $adherents->where('sexe', Adherent::SEXE_FEMININ)->count(),
            'age_moyen' => $adherents->avg('age'),
            'anciennete_moyenne' => $adherents->avg('years_of_service'),
        ];

        // Group by employer
        $byEmployer = $adherents->groupBy('employeur')->map(function ($group, $employer) {
            return [
                'employeur' => $employer,
                'nombre_adherents' => $group->count(),
                'actifs' => $group->where('statut', Adherent::STATUS_ACTIF)->count(),
                'salaire_moyen' => $group->avg('salaire_base'),
            ];
        })->values();

        // Age distribution
        $ageDistribution = [
            '20-30' => $adherents->filter(fn($a) => $a->age >= 20 && $a->age < 30)->count(),
            '30-40' => $adherents->filter(fn($a) => $a->age >= 30 && $a->age < 40)->count(),
            '40-50' => $adherents->filter(fn($a) => $a->age >= 40 && $a->age < 50)->count(),
            '50-60' => $adherents->filter(fn($a) => $a->age >= 50 && $a->age < 60)->count(),
            '60+' => $adherents->filter(fn($a) => $a->age >= 60)->count(),
        ];

        return [
            'statistics' => $statistics,
            'by_employer' => $byEmployer,
            'age_distribution' => $ageDistribution,
            'adherents' => $adherents,
            'filters' => $filters,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate cotisations report
     */
    public function generateCotisationsReport(array $filters = []): array
    {
        $query = Cotisation::with(['adherent']);

        // Apply filters
        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        if (!empty($filters['mois'])) {
            $query->byMonth($filters['mois']);
        }

        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['employeur'])) {
            $query->whereHas('adherent', function ($q) use ($filters) {
                $q->where('employeur', 'LIKE', "%{$filters['employeur']}%");
            });
        }

        $cotisations = $query->get();

        // Statistics
        $statistics = [
            'total_cotisations' => $cotisations->count(),
            'cotisations_payees' => $cotisations->where('statut', Cotisation::STATUS_PAYEE)->count(),
            'cotisations_en_attente' => $cotisations->where('statut', Cotisation::STATUS_EN_ATTENTE)->count(),
            'cotisations_en_retard' => $cotisations->where('statut', Cotisation::STATUS_EN_RETARD)->count(),
            'montant_total' => $cotisations->sum('montant_cotisation'),
            'montant_paye' => $cotisations->where('statut', Cotisation::STATUS_PAYEE)->sum('montant_cotisation'),
            'montant_en_attente' => $cotisations->where('statut', Cotisation::STATUS_EN_ATTENTE)->sum('montant_cotisation'),
            'taux_recouvrement' => $cotisations->count() > 0 
                ? round(($cotisations->where('statut', Cotisation::STATUS_PAYEE)->count() / $cotisations->count()) * 100, 2) 
                : 0,
        ];

        // Monthly evolution
        $monthlyEvolution = [];
        $year = $filters['annee'] ?? date('Y');
        
        for ($month = 1; $month <= 12; $month++) {
            $monthlyCotisations = $cotisations->where('mois_cotisation', $month);
            $monthlyEvolution[] = [
                'month' => $month,
                'month_name' => Carbon::create($year, $month)->format('F'),
                'total_cotisations' => $monthlyCotisations->sum('montant_cotisation'),
                'cotisations_payees' => $monthlyCotisations->where('statut', Cotisation::STATUS_PAYEE)->sum('montant_cotisation'),
                'nombre_cotisations' => $monthlyCotisations->count(),
            ];
        }

        // By payment mode
        $byPaymentMode = $cotisations->where('statut', Cotisation::STATUS_PAYEE)
                                   ->groupBy('mode_paiement')
                                   ->map(function ($group, $mode) {
                                       return [
                                           'mode' => $mode,
                                           'nombre' => $group->count(),
                                           'montant' => $group->sum('montant_cotisation'),
                                       ];
                                   })->values();

        return [
            'statistics' => $statistics,
            'monthly_evolution' => $monthlyEvolution,
            'by_payment_mode' => $byPaymentMode,
            'cotisations' => $cotisations,
            'filters' => $filters,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate pensions report
     */
    public function generatePensionsReport(array $filters = []): array
    {
        $query = DossierPension::with(['adherent', 'preLiquidation']);

        // Apply filters
        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['type_pension'])) {
            $query->byType($filters['type_pension']);
        }

        $dossiers = $query->get();

        // Statistics
        $statistics = [
            'total_dossiers' => $dossiers->count(),
            'dossiers_en_cours' => $dossiers->where('statut', DossierPension::STATUS_EN_COURS)->count(),
            'dossiers_valides' => $dossiers->where('statut', DossierPension::STATUS_VALIDE)->count(),
            'dossiers_liquides' => $dossiers->where('statut', DossierPension::STATUS_LIQUIDE)->count(),
            'montant_total_liquide' => $dossiers->where('statut', DossierPension::STATUS_LIQUIDE)->sum('montant_pension_liquide'),
            'pension_moyenne' => $dossiers->where('statut', DossierPension::STATUS_LIQUIDE)->avg('montant_pension_liquide'),
        ];

        // By pension type
        $byType = [
            'vieillesse' => $dossiers->where('type_pension', DossierPension::TYPE_VIEILLESSE)->count(),
            'invalidite' => $dossiers->where('type_pension', DossierPension::TYPE_INVALIDITE)->count(),
            'survivant' => $dossiers->where('type_pension', DossierPension::TYPE_SURVIVANT)->count(),
            'anticipee' => $dossiers->where('type_pension', DossierPension::TYPE_ANTICIPEE)->count(),
        ];

        // Processing time analysis
        $liquidatedDossiers = $dossiers->where('statut', DossierPension::STATUS_LIQUIDE);
        $processingTimes = $liquidatedDossiers->map(function ($dossier) {
            return $dossier->date_demande->diffInDays($dossier->liquidated_at);
        });

        $processingStats = [
            'average_days' => $processingTimes->avg(),
            'min_days' => $processingTimes->min(),
            'max_days' => $processingTimes->max(),
            'median_days' => $processingTimes->median(),
        ];

        return [
            'statistics' => $statistics,
            'by_type' => $byType,
            'processing_stats' => $processingStats,
            'dossiers' => $dossiers,
            'filters' => $filters,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate financial summary report
     */
    public function generateFinancialSummary(array $filters = []): array
    {
        $year = $filters['annee'] ?? date('Y');

        // Cotisations data
        $cotisations = Cotisation::byYear($year)->get();
        $cotisationsPayees = $cotisations->where('statut', Cotisation::STATUS_PAYEE);

        // Pensions data
        $pensionsLiquides = DossierPension::liquidated()->byYear($year)->get();

        // Financial summary
        $summary = [
            'recettes_cotisations' => $cotisationsPayees->sum('montant_cotisation'),
            'depenses_pensions' => $pensionsLiquides->sum('montant_pension_liquide'),
            'solde' => $cotisationsPayees->sum('montant_cotisation') - $pensionsLiquides->sum('montant_pension_liquide'),
            'taux_couverture' => $pensionsLiquides->sum('montant_pension_liquide') > 0 
                ? round(($cotisationsPayees->sum('montant_cotisation') / $pensionsLiquides->sum('montant_pension_liquide')) * 100, 2)
                : 0,
        ];

        // Monthly breakdown
        $monthlyBreakdown = [];
        for ($month = 1; $month <= 12; $month++) {
            $monthlyCotisations = $cotisationsPayees->where('mois_cotisation', $month)->sum('montant_cotisation');
            $monthlyPensions = $pensionsLiquides->filter(function ($pension) use ($month) {
                return $pension->liquidated_at && $pension->liquidated_at->month == $month;
            })->sum('montant_pension_liquide');

            $monthlyBreakdown[] = [
                'month' => $month,
                'month_name' => Carbon::create($year, $month)->format('F'),
                'recettes' => $monthlyCotisations,
                'depenses' => $monthlyPensions,
                'solde' => $monthlyCotisations - $monthlyPensions,
            ];
        }

        return [
            'summary' => $summary,
            'monthly_breakdown' => $monthlyBreakdown,
            'year' => $year,
            'generated_at' => now(),
        ];
    }

    /**
     * Generate dashboard summary
     */
    public function getDashboardSummary(): array
    {
        $currentYear = date('Y');
        $currentMonth = date('n');

        return [
            'adherents' => [
                'total' => Adherent::count(),
                'actifs' => Adherent::active()->count(),
                'nouveaux_mois' => Adherent::whereYear('date_adhesion', $currentYear)
                                          ->whereMonth('date_adhesion', $currentMonth)
                                          ->count(),
            ],
            'cotisations' => [
                'total_annee' => Cotisation::byYear($currentYear)->sum('montant_cotisation'),
                'payees_annee' => Cotisation::byYear($currentYear)->paid()->sum('montant_cotisation'),
                'en_retard' => Cotisation::overdue()->count(),
            ],
            'pensions' => [
                'dossiers_en_cours' => DossierPension::pending()->count(),
                'liquides_annee' => DossierPension::liquidated()->byYear($currentYear)->count(),
                'montant_liquide_annee' => DossierPension::liquidated()->byYear($currentYear)->sum('montant_pension_liquide'),
            ],
        ];
    }
}
