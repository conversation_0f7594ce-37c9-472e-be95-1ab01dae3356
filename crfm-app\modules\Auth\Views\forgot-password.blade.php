<!DOCTYPE html>
<html lang="fr">
<head>
    <title>CRFM - Mot de passe oublié</title>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <!-- Favicon icon -->
    <link rel="icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
    <!-- Google font-->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,800" rel="stylesheet">
    <!-- Required Framework -->
    <link rel="stylesheet" type="text/css" href="{{ asset('bower_components/bootstrap/dist/css/bootstrap.min.css') }}">
    <!-- Style.css -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">
    <style>
        .auth-wrapper {
            background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
            min-height: 100vh;
        }
        .auth-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .auth-form-light {
            padding: 40px;
        }
        .brand-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .brand-logo h2 {
            color: #1a237e;
            font-weight: 600;
        }
        .btn-primary {
            background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #283593 0%, #1a237e 100%);
        }
    </style>
</head>

<body>
    <div class="auth-wrapper">
        <div class="auth-content">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-5">
                        <div class="auth-form-light">
                            <div class="brand-logo">
                                <h2>CRFM</h2>
                                <p class="text-muted">Récupération de mot de passe</p>
                            </div>
                            
                            @if (session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif

                            @if ($errors->any())
                                <div class="alert alert-danger">
                                    @foreach ($errors->all() as $error)
                                        <div>{{ $error }}</div>
                                    @endforeach
                                </div>
                            @endif

                            <div class="alert alert-info">
                                <small>
                                    Entrez votre adresse email pour recevoir un lien de réinitialisation de mot de passe.
                                </small>
                            </div>

                            <form method="POST" action="{{ route('auth.forgot-password') }}">
                                @csrf
                                <div class="form-group">
                                    <label for="email">Adresse Email</label>
                                    <input type="email" 
                                           class="form-control @if($errors->has('email')) is-invalid @endif" 
                                           id="email" 
                                           name="email" 
                                           value="{{ old('email') }}" 
                                           required 
                                           autofocus>
                                    @if($errors->has('email'))
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @endif
                                </div>
                                
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        Envoyer le lien de réinitialisation
                                    </button>
                                </div>
                                
                                <div class="my-2 d-flex justify-content-center">
                                    <a href="{{ route('auth.login') }}" class="auth-link text-black">
                                        Retour à la connexion
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Required Js -->
    <script src="{{ asset('bower_components/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('bower_components/bootstrap/dist/js/bootstrap.min.js') }}"></script>
</body>
</html>
