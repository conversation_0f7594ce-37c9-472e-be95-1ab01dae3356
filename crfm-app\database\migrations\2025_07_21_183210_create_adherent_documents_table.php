<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adherent_documents', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');

            $table->enum('type_document', ['cni', 'passeport', 'acte_naissance', 'certificat_travail', 'bulletin_salaire', 'photo', 'autre']);
            $table->string('nom_document');
            $table->string('chemin_fichier');
            $table->bigInteger('taille_fichier'); // en bytes
            $table->string('type_mime');
            $table->timestamp('date_upload');
            $table->foreignId('uploaded_by')->constrained('users')->onDelete('cascade');
            $table->boolean('is_verified')->default(false);
            $table->text('observations')->nullable();

            $table->timestamps();
            $table->softDeletes();

            $table->index(['adherent_id', 'type_document']);
            $table->index(['is_verified']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adherent_documents');
    }
};
