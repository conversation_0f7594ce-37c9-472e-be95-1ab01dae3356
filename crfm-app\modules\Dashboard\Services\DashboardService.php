<?php

namespace Modules\Dashboard\Services;

use App\Models\User;
use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;
use Modules\Pensions\Models\DossierPension;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Carbon\Carbon;

class DashboardService
{
    /**
     * Get quick statistics for dashboard
     */
    public function getQuickStats(): array
    {
        return Cache::remember('dashboard.quick_stats', 300, function () {
            return [
                'total_adherents' => $this->getTotalAdherents(),
                'adherents_actifs' => $this->getAdherentsActifs(),
                'adherents_suspendus' => $this->getAdherentsSuspendus(),
                'cotisations_mois' => $this->getCotisationsMois(),
                'cotisations_payees' => $this->getCotisationsPayees(),
                'cotisations_en_retard' => $this->getCotisationsEnRetard(),
                'pensions_en_cours' => $this->getPensionsEnCours(),
                'pensions_liquidees' => $this->getPensionsLiquidees(),
                'utilisateurs_actifs' => $this->getUtilisateursActifs(),
                'montant_total_cotisations' => $this->getMontantTotalCotisations(),
            ];
        });
    }

    /**
     * Get recent activities (real data)
     */
    public function getRecentActivities(): array
    {
        return Cache::remember('dashboard.recent_activities', 300, function () {
            $activities = [];

            try {
                // Récupérer les dernières cotisations
                $recentCotisations = Cotisation::with('adherent')
                                             ->orderBy('created_at', 'desc')
                                             ->limit(3)
                                             ->get();

                foreach ($recentCotisations as $cotisation) {
                    $activities[] = [
                        'type' => 'cotisation',
                        'message' => 'Nouvelle cotisation de ' . number_format($cotisation->montant_cotisation) . '€ pour ' . ($cotisation->adherent->nom ?? 'Adhérent'),
                        'time' => $cotisation->created_at,
                        'icon' => 'feather icon-dollar-sign',
                        'color' => 'success'
                    ];
                }

                // Récupérer les derniers adhérents
                $recentAdherents = Adherent::orderBy('created_at', 'desc')
                                         ->limit(2)
                                         ->get();

                foreach ($recentAdherents as $adherent) {
                    $activities[] = [
                        'type' => 'adherent',
                        'message' => 'Nouvel adhérent inscrit : ' . $adherent->nom . ' ' . $adherent->prenom,
                        'time' => $adherent->created_at,
                        'icon' => 'feather icon-user-plus',
                        'color' => 'primary'
                    ];
                }

                // Récupérer les derniers dossiers de pension
                $recentPensions = DossierPension::with('adherent')
                                               ->orderBy('updated_at', 'desc')
                                               ->limit(2)
                                               ->get();

                foreach ($recentPensions as $pension) {
                    $activities[] = [
                        'type' => 'pension',
                        'message' => 'Dossier de pension ' . $pension->statut . ' pour ' . ($pension->adherent->nom ?? 'Adhérent'),
                        'time' => $pension->updated_at,
                        'icon' => 'feather icon-briefcase',
                        'color' => 'info'
                    ];
                }

                // Trier par date décroissante et limiter à 5
                usort($activities, function($a, $b) {
                    return $b['time']->timestamp - $a['time']->timestamp;
                });

                return array_slice($activities, 0, 5);

            } catch (\Exception $e) {
                // Fallback en cas d'erreur
                return [
                    [
                        'type' => 'system',
                        'message' => 'Système démarré avec succès',
                        'time' => now(),
                        'icon' => 'feather icon-check-circle',
                        'color' => 'success'
                    ]
                ];
            }
        });
    }

    /**
     * Get chart data
     */
    public function getChartData(string $type = 'cotisations', string $period = 'month'): array
    {
        $cacheKey = "dashboard.chart_data.{$type}.{$period}";
        
        return Cache::remember($cacheKey, 300, function () use ($type, $period) {
            $labels = $this->getLabelsForPeriod($period);

            return match($type) {
                'cotisations' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Cotisations (€)',
                            'data' => $this->getCotisationsChartData($period),
                            'backgroundColor' => 'rgba(26, 35, 126, 0.1)',
                            'borderColor' => 'rgba(26, 35, 126, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                'pensions' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Pensions versées (€)',
                            'data' => $this->getPensionsChartData($period),
                            'backgroundColor' => 'rgba(40, 53, 147, 0.1)',
                            'borderColor' => 'rgba(40, 53, 147, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                'adherents' => [
                    'labels' => $labels,
                    'datasets' => [
                        [
                            'label' => 'Nouveaux adhérents',
                            'data' => $this->getAdherentsChartData($period),
                            'backgroundColor' => 'rgba(76, 175, 80, 0.1)',
                            'borderColor' => 'rgba(76, 175, 80, 1)',
                            'borderWidth' => 2,
                            'fill' => true
                        ]
                    ]
                ],
                default => []
            };
        });
    }

    /**
     * Get pending tasks
     */
    public function getPendingTasks(): array
    {
        return Cache::remember('dashboard.pending_tasks', 300, function () {
            // Données simulées pour les tâches en attente
            return [
                [
                    'title' => 'Validation des cotisations de janvier',
                    'priority' => 'high',
                    'due_date' => now()->addDays(2),
                    'assigned_to' => 'Équipe Cotisations'
                ],
                [
                    'title' => 'Traitement des dossiers de pension',
                    'priority' => 'medium',
                    'due_date' => now()->addWeek(),
                    'assigned_to' => 'Équipe Pensions'
                ],
                [
                    'title' => 'Mise à jour des données adhérents',
                    'priority' => 'low',
                    'due_date' => now()->addWeeks(2),
                    'assigned_to' => 'Équipe Adhérents'
                ]
            ];
        });
    }

    /**
     * Get notifications
     */
    public function getNotifications(): array
    {
        return Cache::remember('dashboard.notifications', 300, function () {
            return [
                [
                    'title' => 'Rappel de cotisation',
                    'message' => '15 adhérents ont des cotisations en retard',
                    'type' => 'warning',
                    'time' => now()->subHours(1)
                ],
                [
                    'title' => 'Nouveau dossier de pension',
                    'message' => '3 nouveaux dossiers à traiter',
                    'type' => 'info',
                    'time' => now()->subHours(3)
                ]
            ];
        });
    }

    /**
     * Get total adherents (real data)
     */
    private function getTotalAdherents(): int
    {
        try {
            return Adherent::count();
        } catch (\Exception $e) {
            // Fallback en cas d'erreur
            return 0;
        }
    }

    /**
     * Get cotisations for current month (real data)
     */
    private function getCotisationsMois(): int
    {
        try {
            return Cotisation::whereMonth('created_at', Carbon::now()->month)
                           ->whereYear('created_at', Carbon::now()->year)
                           ->sum('montant_cotisation');
        } catch (\Exception $e) {
            // Fallback en cas d'erreur
            return 0;
        }
    }

    /**
     * Get pensions en cours (real data)
     */
    private function getPensionsEnCours(): int
    {
        try {
            return DossierPension::whereIn('statut', ['en_cours', 'en_attente', 'en_traitement'])
                                ->count();
        } catch (\Exception $e) {
            // Fallback en cas d'erreur
            return 0;
        }
    }

    /**
     * Get active users
     */
    private function getUtilisateursActifs(): int
    {
        return User::count();
    }

    /**
     * Get adherents actifs (real data)
     */
    private function getAdherentsActifs(): int
    {
        try {
            return Adherent::where('statut', 'actif')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get adherents suspendus (real data)
     */
    private function getAdherentsSuspendus(): int
    {
        try {
            return Adherent::where('statut', 'suspendu')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cotisations payées (real data)
     */
    private function getCotisationsPayees(): int
    {
        try {
            return Cotisation::where('statut', 'payee')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get cotisations en retard (real data)
     */
    private function getCotisationsEnRetard(): int
    {
        try {
            return Cotisation::where('statut', 'en_retard')
                           ->orWhere(function($query) {
                               $query->where('statut', 'en_attente')
                                     ->where('date_echeance', '<', Carbon::now());
                           })
                           ->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get pensions liquidées (real data)
     */
    private function getPensionsLiquidees(): int
    {
        try {
            return DossierPension::where('statut', 'liquide')->count();
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get montant total des cotisations (real data)
     */
    private function getMontantTotalCotisations(): int
    {
        try {
            return Cotisation::where('statut', 'payee')->sum('montant_cotisation');
        } catch (\Exception $e) {
            return 0;
        }
    }

    /**
     * Get labels for chart period
     */
    private function getLabelsForPeriod(string $period): array
    {
        return match($period) {
            'week' => ['Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam', 'Dim'],
            'month' => ['Sem 1', 'Sem 2', 'Sem 3', 'Sem 4', 'Sem 5', 'Sem 6'],
            'year' => ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun', 'Jul', 'Aoû', 'Sep', 'Oct', 'Nov', 'Déc'],
            default => ['Jan', 'Fév', 'Mar', 'Avr', 'Mai', 'Jun']
        };
    }

    /**
     * Get cotisations chart data (real data)
     */
    private function getCotisationsChartData(string $period): array
    {
        try {
            $data = [];
            $now = Carbon::now();

            if ($period === 'year') {
                // Données par mois pour l'année courante
                for ($i = 1; $i <= 12; $i++) {
                    $montant = Cotisation::whereMonth('created_at', $i)
                                       ->whereYear('created_at', $now->year)
                                       ->sum('montant_cotisation');
                    $data[] = $montant ?: 0;
                }
            } elseif ($period === 'month') {
                // Données par semaine pour le mois courant
                for ($i = 1; $i <= 6; $i++) {
                    $startWeek = $now->copy()->startOfMonth()->addWeeks($i - 1);
                    $endWeek = $startWeek->copy()->addWeek();

                    $montant = Cotisation::whereBetween('created_at', [$startWeek, $endWeek])
                                       ->sum('montant_cotisation');
                    $data[] = $montant ?: 0;
                }
            } else {
                // Données par jour pour la semaine courante
                for ($i = 0; $i < 7; $i++) {
                    $day = $now->copy()->startOfWeek()->addDays($i);
                    $montant = Cotisation::whereDate('created_at', $day)
                                       ->sum('montant_cotisation');
                    $data[] = $montant ?: 0;
                }
            }

            return $data;
        } catch (\Exception $e) {
            // Fallback avec des données par défaut
            return $period === 'year' ? array_fill(0, 12, 0) : array_fill(0, 6, 0);
        }
    }

    /**
     * Get pensions chart data (real data)
     */
    private function getPensionsChartData(string $period): array
    {
        try {
            $data = [];
            $now = Carbon::now();

            if ($period === 'year') {
                // Nombre de pensions liquidées par mois
                for ($i = 1; $i <= 12; $i++) {
                    $count = DossierPension::where('statut', 'liquide')
                                         ->whereMonth('updated_at', $i)
                                         ->whereYear('updated_at', $now->year)
                                         ->count();
                    $data[] = $count * 1200; // Estimation moyenne par pension
                }
            } elseif ($period === 'month') {
                // Données par semaine
                for ($i = 1; $i <= 6; $i++) {
                    $startWeek = $now->copy()->startOfMonth()->addWeeks($i - 1);
                    $endWeek = $startWeek->copy()->addWeek();

                    $count = DossierPension::where('statut', 'liquide')
                                         ->whereBetween('updated_at', [$startWeek, $endWeek])
                                         ->count();
                    $data[] = $count * 1200;
                }
            } else {
                // Données par jour
                for ($i = 0; $i < 7; $i++) {
                    $day = $now->copy()->startOfWeek()->addDays($i);
                    $count = DossierPension::where('statut', 'liquide')
                                         ->whereDate('updated_at', $day)
                                         ->count();
                    $data[] = $count * 1200;
                }
            }

            return $data;
        } catch (\Exception $e) {
            return $period === 'year' ? array_fill(0, 12, 0) : array_fill(0, 6, 0);
        }
    }

    /**
     * Get adherents chart data (real data)
     */
    private function getAdherentsChartData(string $period): array
    {
        try {
            $data = [];
            $now = Carbon::now();

            if ($period === 'year') {
                // Nouveaux adhérents par mois
                for ($i = 1; $i <= 12; $i++) {
                    $count = Adherent::whereMonth('created_at', $i)
                                   ->whereYear('created_at', $now->year)
                                   ->count();
                    $data[] = $count;
                }
            } elseif ($period === 'month') {
                // Nouveaux adhérents par semaine
                for ($i = 1; $i <= 6; $i++) {
                    $startWeek = $now->copy()->startOfMonth()->addWeeks($i - 1);
                    $endWeek = $startWeek->copy()->addWeek();

                    $count = Adherent::whereBetween('created_at', [$startWeek, $endWeek])
                                   ->count();
                    $data[] = $count;
                }
            } else {
                // Nouveaux adhérents par jour
                for ($i = 0; $i < 7; $i++) {
                    $day = $now->copy()->startOfWeek()->addDays($i);
                    $count = Adherent::whereDate('created_at', $day)
                                   ->count();
                    $data[] = $count;
                }
            }

            return $data;
        } catch (\Exception $e) {
            return $period === 'year' ? array_fill(0, 12, 0) : array_fill(0, 6, 0);
        }
    }
}
