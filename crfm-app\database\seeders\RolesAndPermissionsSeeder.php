<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Modules\Auth\Models\Role;
use Modules\Auth\Models\Permission;
use Illuminate\Support\Str;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create permissions
        $permissions = [
            // Dashboard
            ['name' => 'Voir le tableau de bord', 'slug' => 'view-dashboard', 'module' => 'dashboard'],

            // Auth/Users
            ['name' => 'Gérer les utilisateurs', 'slug' => 'manage-users', 'module' => 'auth'],
            ['name' => 'Gérer les rôles', 'slug' => 'manage-roles', 'module' => 'auth'],

            // Adhérents
            ['name' => 'Voir les adhérents', 'slug' => 'view-adherents', 'module' => 'adherents'],
            ['name' => 'Gérer les adhérents', 'slug' => 'manage-adherents', 'module' => 'adherents'],

            // Cotisations
            ['name' => 'Voir les cotisations', 'slug' => 'view-cotisations', 'module' => 'cotisations'],
            ['name' => 'Gérer les cotisations', 'slug' => 'manage-cotisations', 'module' => 'cotisations'],

            // Pensions
            ['name' => 'Voir les pensions', 'slug' => 'view-pensions', 'module' => 'pensions'],
            ['name' => 'Gérer les pensions', 'slug' => 'manage-pensions', 'module' => 'pensions'],

            // Rapports
            ['name' => 'Voir les rapports', 'slug' => 'view-reports', 'module' => 'reports'],
            ['name' => 'Générer les rapports', 'slug' => 'generate-reports', 'module' => 'reports'],
        ];

        foreach ($permissions as $permission) {
            Permission::create([
                'uuid' => Str::uuid(),
                'name' => $permission['name'],
                'slug' => $permission['slug'],
                'module' => $permission['module'],
                'is_active' => true,
            ]);
        }

        // Create roles
        $adminRole = Role::create([
            'uuid' => Str::uuid(),
            'name' => 'Administrateur',
            'slug' => 'admin',
            'description' => 'Accès complet à toutes les fonctionnalités',
            'level' => 3,
            'is_active' => true,
        ]);

        $gestionnaireRole = Role::create([
            'uuid' => Str::uuid(),
            'name' => 'Gestionnaire',
            'slug' => 'gestionnaire',
            'description' => 'Gestion des adhérents, cotisations et pensions',
            'level' => 2,
            'is_active' => true,
        ]);

        $consultantRole = Role::create([
            'uuid' => Str::uuid(),
            'name' => 'Consultant',
            'slug' => 'consultant',
            'description' => 'Consultation des données uniquement',
            'level' => 1,
            'is_active' => true,
        ]);

        // Assign permissions to roles
        $allPermissions = Permission::all();

        // Admin gets all permissions
        $adminRole->permissions()->attach($allPermissions->pluck('id'));

        // Gestionnaire gets most permissions except user management
        $gestionnairePermissions = $allPermissions->whereNotIn('slug', ['manage-users', 'manage-roles']);
        $gestionnaireRole->permissions()->attach($gestionnairePermissions->pluck('id'));

        // Consultant gets only view permissions
        $consultantPermissions = $allPermissions->where('slug', 'like', 'view-%');
        $consultantRole->permissions()->attach($consultantPermissions->pluck('id'));
    }
}
