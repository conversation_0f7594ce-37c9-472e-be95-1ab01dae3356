@extends('layouts.app')

@section('title', 'Test des Fonctions JavaScript')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Test des Fonctions JavaScript CRFM</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active">Test JavaScript</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Test du système de notifications -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test du Système de Notifications</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-primary btn-block" onclick="testToast('success')">
                                Toast Succès
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-warning btn-block" onclick="testToast('warning')">
                                Toast Avertissement
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-danger btn-block" onclick="testToast('error')">
                                Toast Erreur
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-info btn-block" onclick="testToast('info')">
                                Toast Info
                            </button>
                        </div>
                    </div>
                    <hr>
                    <div class="row">
                        <div class="col-md-6 mb-2">
                            <button type="button" class="btn btn-secondary btn-block" onclick="testNotificationLoad()">
                                Charger Notifications
                            </button>
                        </div>
                        <div class="col-md-6 mb-2">
                            <button type="button" class="btn btn-secondary btn-block" onclick="testNotificationSend()">
                                Envoyer Notification Test
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test des fonctions Cotisations -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test des Fonctions Cotisations</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-success btn-block" onclick="testMarkAsPaid()">
                                Marquer comme Payé
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-warning btn-block" onclick="testSendReminder()">
                                Envoyer Relance
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-info btn-block" onclick="testPrintCotisation()">
                                Imprimer Cotisation
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-danger btn-block" onclick="testDeleteCotisation()">
                                Supprimer Cotisation
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test des fonctions Pensions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test des Fonctions Pensions</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-primary btn-block" onclick="testAdvanceWorkflow()">
                                Avancer Workflow
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-success btn-block" onclick="testCalculatePension()">
                                Calculer Pension
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-info btn-block" onclick="testPrintDossier()">
                                Imprimer Dossier
                            </button>
                        </div>
                        <div class="col-md-3 mb-2">
                            <button type="button" class="btn btn-warning btn-block" onclick="testLiquidateDossier()">
                                Liquider Dossier
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test des fonctions globales -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Test des Fonctions Globales</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-secondary btn-block" onclick="testCRFMNamespace()">
                                Test Namespace CRFM
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-secondary btn-block" onclick="testFormatCurrency()">
                                Test Format Monnaie
                            </button>
                        </div>
                        <div class="col-md-4 mb-2">
                            <button type="button" class="btn btn-secondary btn-block" onclick="testValidateForm()">
                                Test Validation Formulaire
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Résultats des tests -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Résultats des Tests</h5>
                </div>
                <div class="card-body">
                    <div id="test-results">
                        <p class="text-muted">Cliquez sur les boutons ci-dessus pour tester les fonctions</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
console.log('Page de test JavaScript chargée');

let testResults = [];

/**
 * Ajouter un résultat de test
 */
function addTestResult(testName, success, message) {
    const timestamp = new Date().toLocaleTimeString();
    const status = success ? '✅' : '❌';
    const color = success ? 'text-success' : 'text-danger';
    
    testResults.unshift({
        name: testName,
        success: success,
        message: message,
        timestamp: timestamp
    });
    
    updateTestResults();
}

/**
 * Mettre à jour l'affichage des résultats
 */
function updateTestResults() {
    const container = document.getElementById('test-results');
    
    if (testResults.length === 0) {
        container.innerHTML = '<p class="text-muted">Aucun test exécuté</p>';
        return;
    }
    
    let html = '<div class="list-group">';
    testResults.slice(0, 10).forEach(result => {
        const status = result.success ? '✅' : '❌';
        const color = result.success ? 'text-success' : 'text-danger';
        
        html += `
            <div class="list-group-item">
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${status} ${result.name}</h6>
                    <small>${result.timestamp}</small>
                </div>
                <p class="mb-1 ${color}">${result.message}</p>
            </div>
        `;
    });
    html += '</div>';
    
    container.innerHTML = html;
}

// Tests du système de notifications
function testToast(type) {
    try {
        if (window.CRFMNotifications) {
            window.CRFMNotifications.showToast(`Test toast ${type}`, type);
            addTestResult(`Toast ${type}`, true, 'Toast affiché avec succès');
        } else {
            throw new Error('CRFMNotifications non disponible');
        }
    } catch (e) {
        addTestResult(`Toast ${type}`, false, e.message);
    }
}

function testNotificationLoad() {
    try {
        if (window.CRFMNotifications) {
            window.CRFMNotifications.loadUnreadNotifications();
            addTestResult('Chargement notifications', true, 'Chargement des notifications initié');
        } else {
            throw new Error('CRFMNotifications non disponible');
        }
    } catch (e) {
        addTestResult('Chargement notifications', false, e.message);
    }
}

function testNotificationSend() {
    try {
        if (typeof sendNotificationToAdherent === 'function') {
            // Test avec un ID fictif
            sendNotificationToAdherent(1);
            addTestResult('Envoi notification adhérent', true, 'Modal d\'envoi ouvert');
        } else {
            throw new Error('Fonction sendNotificationToAdherent non disponible');
        }
    } catch (e) {
        addTestResult('Envoi notification adhérent', false, e.message);
    }
}

// Tests des fonctions Cotisations
function testMarkAsPaid() {
    try {
        if (typeof markAsPaid === 'function') {
            markAsPaid(1); // Test avec ID fictif
            addTestResult('Marquer comme payé', true, 'Modal de paiement ouvert');
        } else {
            throw new Error('Fonction markAsPaid non disponible');
        }
    } catch (e) {
        addTestResult('Marquer comme payé', false, e.message);
    }
}

function testSendReminder() {
    try {
        if (typeof sendReminder === 'function') {
            // Simuler la fonction sans vraiment l'exécuter
            addTestResult('Envoyer relance', true, 'Fonction sendReminder disponible');
        } else {
            throw new Error('Fonction sendReminder non disponible');
        }
    } catch (e) {
        addTestResult('Envoyer relance', false, e.message);
    }
}

function testPrintCotisation() {
    try {
        if (typeof printCotisation === 'function') {
            addTestResult('Imprimer cotisation', true, 'Fonction printCotisation disponible');
        } else {
            throw new Error('Fonction printCotisation non disponible');
        }
    } catch (e) {
        addTestResult('Imprimer cotisation', false, e.message);
    }
}

function testDeleteCotisation() {
    try {
        if (typeof deleteCotisation === 'function') {
            addTestResult('Supprimer cotisation', true, 'Fonction deleteCotisation disponible');
        } else {
            throw new Error('Fonction deleteCotisation non disponible');
        }
    } catch (e) {
        addTestResult('Supprimer cotisation', false, e.message);
    }
}

// Tests des fonctions Pensions
function testAdvanceWorkflow() {
    try {
        if (typeof advanceWorkflow === 'function') {
            addTestResult('Avancer workflow', true, 'Fonction advanceWorkflow disponible');
        } else {
            throw new Error('Fonction advanceWorkflow non disponible');
        }
    } catch (e) {
        addTestResult('Avancer workflow', false, e.message);
    }
}

function testCalculatePension() {
    try {
        if (typeof calculatePension === 'function') {
            addTestResult('Calculer pension', true, 'Fonction calculatePension disponible');
        } else {
            throw new Error('Fonction calculatePension non disponible');
        }
    } catch (e) {
        addTestResult('Calculer pension', false, e.message);
    }
}

function testPrintDossier() {
    try {
        if (typeof printDossier === 'function') {
            addTestResult('Imprimer dossier', true, 'Fonction printDossier disponible');
        } else {
            throw new Error('Fonction printDossier non disponible');
        }
    } catch (e) {
        addTestResult('Imprimer dossier', false, e.message);
    }
}

function testLiquidateDossier() {
    try {
        if (typeof liquidateDossier === 'function') {
            liquidateDossier(1); // Test avec ID fictif
            addTestResult('Liquider dossier', true, 'Modal de liquidation ouvert');
        } else {
            throw new Error('Fonction liquidateDossier non disponible');
        }
    } catch (e) {
        addTestResult('Liquider dossier', false, e.message);
    }
}

// Tests des fonctions globales
function testCRFMNamespace() {
    try {
        if (window.CRFM) {
            addTestResult('Namespace CRFM', true, 'Namespace CRFM disponible');
        } else {
            throw new Error('Namespace CRFM non disponible');
        }
    } catch (e) {
        addTestResult('Namespace CRFM', false, e.message);
    }
}

function testFormatCurrency() {
    try {
        if (window.CRFM && typeof window.CRFM.formatCurrency === 'function') {
            const result = window.CRFM.formatCurrency(1234.56);
            addTestResult('Format monnaie', true, `Résultat: ${result}`);
        } else {
            throw new Error('Fonction formatCurrency non disponible');
        }
    } catch (e) {
        addTestResult('Format monnaie', false, e.message);
    }
}

function testValidateForm() {
    try {
        if (window.CRFM && typeof window.CRFM.validateForm === 'function') {
            addTestResult('Validation formulaire', true, 'Fonction validateForm disponible');
        } else {
            throw new Error('Fonction validateForm non disponible');
        }
    } catch (e) {
        addTestResult('Validation formulaire', false, e.message);
    }
}

// Test automatique au chargement
$(document).ready(function() {
    // Vérifier que jQuery est disponible
    addTestResult('jQuery', true, 'jQuery chargé et fonctionnel');
    
    // Vérifier que Bootstrap est disponible
    if (typeof $ !== 'undefined' && $.fn.modal) {
        addTestResult('Bootstrap', true, 'Bootstrap chargé et fonctionnel');
    } else {
        addTestResult('Bootstrap', false, 'Bootstrap non disponible ou incomplet');
    }
    
    // Vérifier le token CSRF
    const token = $('meta[name="csrf-token"]').attr('content');
    if (token) {
        addTestResult('Token CSRF', true, 'Token CSRF présent');
    } else {
        addTestResult('Token CSRF', false, 'Token CSRF manquant');
    }
});
</script>
@endpush
