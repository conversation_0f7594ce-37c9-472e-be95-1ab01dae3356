<?php

namespace Modules\Cotisations\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Cotisations\Models\Cotisation;
use Modules\Cotisations\Services\CotisationService;
use Modules\Cotisations\Repositories\CotisationRepository;
use Modules\Cotisations\Requests\CreateCotisationRequest;
use Modules\Cotisations\Requests\UpdateCotisationRequest;
use Modules\Adherents\Models\Adherent;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class CotisationController extends BaseController
{
    protected CotisationService $cotisationService;
    protected CotisationRepository $cotisationRepository;

    public function __construct(CotisationService $cotisationService, CotisationRepository $cotisationRepository)
    {
        $this->cotisationService = $cotisationService;
        $this->cotisationRepository = $cotisationRepository;
    }

    /**
     * Display a listing of cotisations
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['search', 'statut', 'annee', 'mois', 'adherent_id', 'date_from', 'date_to']);
        $cotisations = $this->cotisationRepository->getPaginatedCotisations($filters);
        $statistics = $this->cotisationService->getStatistics();

        return view('cotisations::index', compact('cotisations', 'statistics', 'filters'));
    }

    /**
     * Show the form for creating a new cotisation
     */
    public function create(): View
    {
        $adherents = Adherent::active()->orderBy('nom')->get();
        
        return view('cotisations::create', compact('adherents'));
    }

    /**
     * Store a newly created cotisation
     */
    public function store(CreateCotisationRequest $request): RedirectResponse
    {
        try {
            $cotisation = $this->cotisationService->createCotisation($request->validated());
            
            return $this->successRedirect('cotisations.show', 'Cotisation créée avec succès !')
                        ->with('cotisation', $cotisation);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la création de la cotisation : ' . $e->getMessage());
        }
    }

    /**
     * Display the specified cotisation
     */
    public function show(Cotisation $cotisation): View
    {
        $cotisation->load(['adherent', 'echeanciers', 'relances', 'creator', 'validator']);
        
        return view('cotisations::show', compact('cotisation'));
    }

    /**
     * Show the form for editing the specified cotisation
     */
    public function edit(Cotisation $cotisation): View
    {
        $adherents = Adherent::active()->orderBy('nom')->get();
        
        return view('cotisations::edit', compact('cotisation', 'adherents'));
    }

    /**
     * Update the specified cotisation
     */
    public function update(UpdateCotisationRequest $request, Cotisation $cotisation): RedirectResponse
    {
        try {
            $this->cotisationService->updateCotisation($cotisation, $request->validated());
            
            return $this->successRedirect('cotisations.show', 'Cotisation mise à jour avec succès !')
                        ->with('cotisation', $cotisation);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified cotisation
     */
    public function destroy(Cotisation $cotisation): RedirectResponse
    {
        try {
            $cotisation->delete();
            
            return $this->successRedirect('cotisations.index', 'Cotisation supprimée avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la suppression : ' . $e->getMessage());
        }
    }

    /**
     * Mark cotisation as paid
     */
    public function markAsPaid(Request $request, Cotisation $cotisation): RedirectResponse
    {
        $request->validate([
            'mode_paiement' => 'required|in:especes,cheque,virement,mobile_money,prelevement',
            'reference_paiement' => 'nullable|string|max:255'
        ]);

        try {
            $this->cotisationService->markAsPaid(
                $cotisation,
                $request->mode_paiement,
                $request->reference_paiement
            );
            
            return $this->backWithSuccess('Cotisation marquée comme payée !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors du marquage : ' . $e->getMessage());
        }
    }

    /**
     * Cancel cotisation
     */
    public function cancel(Request $request, Cotisation $cotisation): RedirectResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        try {
            $this->cotisationService->cancelCotisation($cotisation, $request->reason);
            
            return $this->backWithSuccess('Cotisation annulée avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de l\'annulation : ' . $e->getMessage());
        }
    }

    /**
     * Show generate cotisations form
     */
    public function showGenerate(): View
    {
        $adherents = Adherent::active()->orderBy('nom')->get();
        
        return view('cotisations::generate', compact('adherents'));
    }

    /**
     * Generate cotisations for period
     */
    public function generate(Request $request): RedirectResponse
    {
        $request->validate([
            'periode' => 'required|string|regex:/^\d{4}-\d{2}$/',
            'adherent_ids' => 'nullable|array',
            'adherent_ids.*' => 'exists:adherents,id'
        ]);

        try {
            $results = $this->cotisationService->generateCotisationsForPeriod(
                $request->periode,
                $request->adherent_ids ?? []
            );
            
            $message = "Génération terminée : {$results['success']} cotisations créées";
            if (!empty($results['errors'])) {
                $message .= ", " . count($results['errors']) . " erreurs";
            }
            
            return $this->successRedirect('cotisations.index', $message);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la génération : ' . $e->getMessage());
        }
    }

    /**
     * Show overdue cotisations
     */
    public function overdue(): View
    {
        $cotisations = $this->cotisationService->getOverdueCotisations();
        
        return view('cotisations::overdue', compact('cotisations'));
    }

    /**
     * Process automatic relances
     */
    public function processRelances(): RedirectResponse
    {
        try {
            $results = $this->cotisationService->processAutomaticRelances();
            
            $message = "Relances traitées : {$results['processed']} envoyées";
            if (!empty($results['errors'])) {
                $message .= ", " . count($results['errors']) . " erreurs";
            }
            
            return $this->backWithSuccess($message);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors du traitement des relances : ' . $e->getMessage());
        }
    }

    /**
     * Export cotisations
     */
    public function export(Request $request)
    {
        $filters = $request->only(['statut', 'annee', 'mois', 'adherent_id']);
        $data = $this->cotisationService->exportCotisations($filters);

        $filename = 'cotisations_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            // Add headers
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]), ';');
                
                // Add data
                foreach ($data as $row) {
                    fputcsv($file, $row, ';');
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show import form
     */
    public function showImport(): View
    {
        return view('cotisations::import');
    }

    /**
     * Import cotisations from file
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx|max:10240' // 10MB max
        ]);

        try {
            // Process file and import data
            // This would require additional CSV/Excel processing logic
            
            return $this->successRedirect('cotisations.index', 'Import réalisé avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de l\'import : ' . $e->getMessage());
        }
    }

    /**
     * Get cotisation data for AJAX
     */
    public function getCotisationData(Cotisation $cotisation)
    {
        $cotisation->load(['adherent', 'echeanciers', 'relances']);
        
        return $this->successResponse('Données récupérées', [
            'cotisation' => $cotisation,
            'statistics' => [
                'echeanciers_count' => $cotisation->echeanciers()->count(),
                'echeanciers_paid' => $cotisation->echeanciers()->paid()->count(),
                'relances_count' => $cotisation->relances()->count(),
                'last_relance' => $cotisation->relances()->latest()->first(),
            ]
        ]);
    }

    /**
     * Get dashboard statistics for AJAX
     */
    public function getDashboardStats()
    {
        $statistics = $this->cotisationService->getStatistics();
        
        return $this->successResponse('Statistiques récupérées', $statistics);
    }
}
