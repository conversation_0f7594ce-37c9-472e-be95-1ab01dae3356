<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Cotisation {{ $cotisation->numero_cotisation }} - CRFM</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .subtitle {
            font-size: 14px;
            color: #666;
        }
        .document-title {
            text-align: center;
            font-size: 20px;
            font-weight: bold;
            margin: 30px 0;
            text-transform: uppercase;
        }
        .info-section {
            margin-bottom: 25px;
        }
        .info-title {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 10px;
            color: #007bff;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .info-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }
        .info-item {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: bold;
            display: inline-block;
            width: 150px;
        }
        .info-value {
            color: #555;
        }
        .amount-section {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .amount-grid {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            text-align: center;
        }
        .amount-item {
            padding: 15px;
            background: white;
            border-radius: 5px;
            border: 1px solid #ddd;
        }
        .amount-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            margin-bottom: 5px;
        }
        .amount-value {
            font-size: 18px;
            font-weight: bold;
            color: #007bff;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
            text-transform: uppercase;
        }
        .status-payee { background-color: #d4edda; color: #155724; }
        .status-en-attente { background-color: #fff3cd; color: #856404; }
        .status-en-retard { background-color: #f8d7da; color: #721c24; }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #eee;
            padding-top: 20px;
        }
        .signature-section {
            margin-top: 40px;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 40px;
        }
        .signature-box {
            text-align: center;
            padding: 20px;
            border: 1px dashed #ccc;
            min-height: 80px;
        }
        @media print {
            body { margin: 0; }
            .no-print { display: none; }
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="logo">CRFM - Caisse de Retraite des Fonctionnaires de Mayotte</div>
        <div class="subtitle">Système de Gestion des Cotisations</div>
    </div>

    <div class="document-title">
        Reçu de Cotisation N° {{ $cotisation->numero_cotisation }}
    </div>

    <div class="info-section">
        <div class="info-title">Informations de l'Adhérent</div>
        <div class="info-grid">
            <div>
                <div class="info-item">
                    <span class="info-label">Nom complet :</span>
                    <span class="info-value">{{ $cotisation->adherent->full_name }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">N° Adhérent :</span>
                    <span class="info-value">{{ $cotisation->adherent->numero_adherent }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Email :</span>
                    <span class="info-value">{{ $cotisation->adherent->email ?? 'Non renseigné' }}</span>
                </div>
            </div>
            <div>
                <div class="info-item">
                    <span class="info-label">Téléphone :</span>
                    <span class="info-value">{{ $cotisation->adherent->telephone ?? 'Non renseigné' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Employeur :</span>
                    <span class="info-value">{{ $cotisation->adherent->employeur ?? 'Non renseigné' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Statut :</span>
                    <span class="info-value">{{ $cotisation->adherent->status_label }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="info-section">
        <div class="info-title">Détails de la Cotisation</div>
        <div class="info-grid">
            <div>
                <div class="info-item">
                    <span class="info-label">Période :</span>
                    <span class="info-value">{{ $cotisation->periode_cotisation }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Date d'échéance :</span>
                    <span class="info-value">{{ $cotisation->date_echeance->format('d/m/Y') }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Statut :</span>
                    <span class="status-badge status-{{ str_replace(' ', '-', strtolower($cotisation->status_label)) }}">
                        {{ $cotisation->status_label }}
                    </span>
                </div>
            </div>
            <div>
                @if($cotisation->isPaid())
                <div class="info-item">
                    <span class="info-label">Date de paiement :</span>
                    <span class="info-value">{{ $cotisation->date_paiement?->format('d/m/Y') ?? 'Non renseignée' }}</span>
                </div>
                <div class="info-item">
                    <span class="info-label">Mode de paiement :</span>
                    <span class="info-value">{{ ucfirst($cotisation->mode_paiement ?? 'Non renseigné') }}</span>
                </div>
                @if($cotisation->reference_paiement)
                <div class="info-item">
                    <span class="info-label">Référence :</span>
                    <span class="info-value">{{ $cotisation->reference_paiement }}</span>
                </div>
                @endif
                @endif
            </div>
        </div>
    </div>

    <div class="amount-section">
        <div class="info-title">Montants</div>
        <div class="amount-grid">
            <div class="amount-item">
                <div class="amount-label">Montant Base</div>
                <div class="amount-value">{{ number_format($cotisation->montant_base ?? 0, 0, ',', ' ') }} €</div>
            </div>
            <div class="amount-item">
                <div class="amount-label">Taux Cotisation</div>
                <div class="amount-value">{{ $cotisation->taux_cotisation ?? 0 }}%</div>
            </div>
            <div class="amount-item">
                <div class="amount-label">Montant Total</div>
                <div class="amount-value">{{ number_format($cotisation->montant_cotisation, 0, ',', ' ') }} €</div>
            </div>
        </div>
    </div>

    @if($cotisation->commentaire_paiement)
    <div class="info-section">
        <div class="info-title">Commentaire</div>
        <p>{{ $cotisation->commentaire_paiement }}</p>
    </div>
    @endif

    <div class="signature-section">
        <div class="signature-box">
            <strong>Signature de l'Adhérent</strong>
            <br><br><br>
            <small>Date : _______________</small>
        </div>
        <div class="signature-box">
            <strong>Cachet et Signature CRFM</strong>
            <br><br><br>
            <small>Date : {{ now()->format('d/m/Y') }}</small>
        </div>
    </div>

    <div class="footer">
        <p>Document généré le {{ now()->format('d/m/Y à H:i') }}</p>
        <p>CRFM - Caisse de Retraite des Fonctionnaires de Mayotte</p>
        <p>Adresse : [Adresse CRFM] - Téléphone : [Téléphone CRFM] - Email : [Email CRFM]</p>
    </div>

    <script>
        // Auto-print when page loads
        window.onload = function() {
            window.print();
        }
    </script>
</body>
</html>
