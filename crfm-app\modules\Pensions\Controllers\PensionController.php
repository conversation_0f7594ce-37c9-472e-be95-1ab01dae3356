<?php

namespace Modules\Pensions\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Pensions\Models\DossierPension;
use Modules\Pensions\Services\PensionService;
use Modules\Pensions\Repositories\DossierPensionRepository;
use Modules\Adherents\Models\Adherent;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class PensionController extends BaseController
{
    protected PensionService $pensionService;
    protected DossierPensionRepository $dossierRepository;

    public function __construct(PensionService $pensionService, DossierPensionRepository $dossierRepository)
    {
        $this->pensionService = $pensionService;
        $this->dossierRepository = $dossierRepository;
    }

    /**
     * Display a listing of pension dossiers
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['search', 'statut', 'type_pension', 'annee', 'date_from', 'date_to']);
        $dossiers = $this->dossierRepository->getPaginatedDossiers($filters);
        $statistics = $this->pensionService->getStatistics();

        return view('pensions::index', compact('dossiers', 'statistics', 'filters'));
    }

    /**
     * Show the form for creating a new pension dossier
     */
    public function create(): View
    {
        $adherents = Adherent::active()
                            ->whereDoesntHave('dossierPension')
                            ->orderBy('nom')
                            ->get();
        
        return view('pensions::create', compact('adherents'));
    }

    /**
     * Store a newly created pension dossier
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'adherent_id' => 'required|exists:adherents,id',
            'type_pension' => 'required|in:vieillesse,invalidite,survivant,anticipee',
            'date_demande' => 'required|date|before_or_equal:today',
            'date_depart_retraite' => 'required|date|after:date_demande',
            'observations' => 'nullable|string',
        ]);

        try {
            $dossier = $this->pensionService->createDossier($request->validated());
            
            return $this->successRedirect('pensions.show', 'Dossier de pension créé avec succès !')
                        ->with('dossier', $dossier);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la création du dossier : ' . $e->getMessage());
        }
    }

    /**
     * Display the specified pension dossier
     */
    public function show(DossierPension $dossier): View
    {
        $dossier->load([
            'adherent', 
            'preLiquidation', 
            'documents', 
            'workflowSteps' => function($query) {
                $query->orderBy('step_order');
            },
            'creator', 
            'validator'
        ]);
        
        $workflowProgress = $this->dossierRepository->getWorkflowProgress($dossier);
        
        return view('pensions::show', compact('dossier', 'workflowProgress'));
    }

    /**
     * Show the form for editing the specified pension dossier
     */
    public function edit(DossierPension $dossier): View
    {
        return view('pensions::edit', compact('dossier'));
    }

    /**
     * Update the specified pension dossier
     */
    public function update(Request $request, DossierPension $dossier): RedirectResponse
    {
        $request->validate([
            'type_pension' => 'required|in:vieillesse,invalidite,survivant,anticipee',
            'date_demande' => 'required|date|before_or_equal:today',
            'date_depart_retraite' => 'required|date|after:date_demande',
            'observations' => 'nullable|string',
        ]);

        try {
            $this->pensionService->updateDossier($dossier, $request->validated());
            
            return $this->successRedirect('pensions.show', 'Dossier mis à jour avec succès !')
                        ->with('dossier', $dossier);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified pension dossier
     */
    public function destroy(DossierPension $dossier): RedirectResponse
    {
        try {
            $dossier->delete();
            
            return $this->successRedirect('pensions.index', 'Dossier supprimé avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la suppression : ' . $e->getMessage());
        }
    }

    /**
     * Show pre-liquidation form
     */
    public function showPreLiquidation(DossierPension $dossier): View
    {
        if ($dossier->preLiquidation) {
            return redirect()->route('pensions.pre-liquidation.show', $dossier->preLiquidation);
        }

        return view('pensions::pre-liquidation.create', compact('dossier'));
    }

    /**
     * Create pre-liquidation
     */
    public function createPreLiquidation(Request $request, DossierPension $dossier): RedirectResponse
    {
        $request->validate([
            'periodicite_versement' => 'required|in:mensuelle,trimestrielle,semestrielle,annuelle',
            'mode_versement' => 'required|in:virement,cheque,especes,mobile_money',
            'date_effet' => 'required|date',
            'autres_retenues' => 'nullable|numeric|min:0',
        ]);

        try {
            $preLiquidation = $this->pensionService->createPreLiquidation($dossier, $request->validated());
            
            return $this->successRedirect('pensions.pre-liquidation.show', 'Pré-liquidation créée avec succès !')
                        ->with('preLiquidation', $preLiquidation);
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la création de la pré-liquidation : ' . $e->getMessage());
        }
    }

    /**
     * Validate dossier
     */
    public function validateDossier(DossierPension $dossier): RedirectResponse
    {
        try {
            $this->pensionService->validateDossier($dossier);

            return $this->backWithSuccess('Dossier validé avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la validation : ' . $e->getMessage());
        }
    }

    /**
     * Liquidate dossier
     */
    public function liquidate(Request $request, DossierPension $dossier): RedirectResponse
    {
        $request->validate([
            'montant_final' => 'nullable|numeric|min:0'
        ]);

        try {
            $this->pensionService->liquidateDossier($dossier, $request->montant_final);
            
            return $this->backWithSuccess('Dossier liquidé avec succès !');
        } catch (\Exception $e) {
            return $this->backWithError('Erreur lors de la liquidation : ' . $e->getMessage());
        }
    }

    /**
     * Show simulation form
     */
    public function showSimulation(): View
    {
        $adherents = Adherent::active()->orderBy('nom')->get();
        
        return view('pensions::simulation', compact('adherents'));
    }

    /**
     * Calculate pension simulation
     */
    public function simulate(Request $request)
    {
        $request->validate([
            'adherent_id' => 'required|exists:adherents,id',
            'salaire_reference' => 'nullable|numeric|min:0',
            'duree_cotisation_mois' => 'nullable|integer|min:0',
        ]);

        try {
            $adherent = Adherent::findOrFail($request->adherent_id);
            $simulation = $this->pensionService->simulatePension($adherent, $request->only([
                'salaire_reference', 
                'duree_cotisation_mois'
            ]));
            
            return $this->successResponse('Simulation calculée avec succès', [
                'adherent' => $adherent,
                'simulation' => $simulation
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la simulation : ' . $e->getMessage());
        }
    }

    /**
     * Show dossiers ready for liquidation
     */
    public function readyForLiquidation(): View
    {
        $dossiers = $this->pensionService->getDossiersReadyForLiquidation();
        
        return view('pensions::ready-for-liquidation', compact('dossiers'));
    }

    /**
     * Export dossiers
     */
    public function export(Request $request)
    {
        $filters = $request->only(['statut', 'type_pension', 'annee']);
        $data = $this->pensionService->exportDossiers($filters);

        $filename = 'dossiers_pension_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            // Add headers
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]), ';');
                
                // Add data
                foreach ($data as $row) {
                    fputcsv($file, $row, ';');
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Get dashboard statistics for AJAX
     */
    public function getDashboardStats()
    {
        $statistics = $this->pensionService->getStatistics();
        
        return $this->successResponse('Statistiques récupérées', $statistics);
    }
}
