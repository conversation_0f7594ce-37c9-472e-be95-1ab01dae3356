@extends('layouts.app')

@section('title', 'Test des Statistiques Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Test des Statistiques Dashboard</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active">Test Statistiques</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Statistiques en temps réel -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Statistiques Actuelles</h5>
                    <button class="btn btn-primary btn-sm float-right" onclick="loadStats()">
                        <i class="feather icon-refresh-cw"></i> Actualiser
                    </button>
                </div>
                <div class="card-body">
                    <div id="stats-container">
                        <p class="text-muted">Chargement des statistiques...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques formatées comme dans le dashboard -->
    <div class="row" id="dashboard-cards">
        <!-- Les cartes seront générées dynamiquement -->
    </div>

    <!-- Vérification des variables Blade -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Variables Blade (depuis le contrôleur)</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>Variable</th>
                                    <th>Valeur</th>
                                    <th>Formatée</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>total_adherents</td>
                                    <td>{{ $stats['total_adherents'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['total_adherents']) ? number_format($stats['total_adherents']) : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td>adherents_actifs</td>
                                    <td>{{ $stats['adherents_actifs'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['adherents_actifs']) ? number_format($stats['adherents_actifs']) : 'N/A' }}</td>
                                </tr>
                                <tr class="table-warning">
                                    <td><strong>adherents_suspendus</strong></td>
                                    <td><strong>{{ $stats['adherents_suspendus'] ?? 'Non défini' }}</strong></td>
                                    <td><strong>{{ isset($stats['adherents_suspendus']) ? number_format($stats['adherents_suspendus']) : 'N/A' }}</strong></td>
                                </tr>
                                <tr>
                                    <td>cotisations_mois</td>
                                    <td>{{ $stats['cotisations_mois'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['cotisations_mois']) ? number_format($stats['cotisations_mois']) . ' €' : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td>cotisations_payees</td>
                                    <td>{{ $stats['cotisations_payees'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['cotisations_payees']) ? number_format($stats['cotisations_payees']) : 'N/A' }}</td>
                                </tr>
                                <tr class="table-danger">
                                    <td><strong>cotisations_en_retard</strong></td>
                                    <td><strong>{{ $stats['cotisations_en_retard'] ?? 'Non défini' }}</strong></td>
                                    <td><strong>{{ isset($stats['cotisations_en_retard']) ? number_format($stats['cotisations_en_retard']) : 'N/A' }}</strong></td>
                                </tr>
                                <tr class="table-success">
                                    <td><strong>montant_total_cotisations</strong></td>
                                    <td><strong>{{ $stats['montant_total_cotisations'] ?? 'Non défini' }}</strong></td>
                                    <td><strong>{{ isset($stats['montant_total_cotisations']) ? number_format($stats['montant_total_cotisations']) . ' €' : 'N/A' }}</strong></td>
                                </tr>
                                <tr>
                                    <td>pensions_en_cours</td>
                                    <td>{{ $stats['pensions_en_cours'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['pensions_en_cours']) ? number_format($stats['pensions_en_cours']) : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td>pensions_liquidees</td>
                                    <td>{{ $stats['pensions_liquidees'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['pensions_liquidees']) ? number_format($stats['pensions_liquidees']) : 'N/A' }}</td>
                                </tr>
                                <tr>
                                    <td>utilisateurs_actifs</td>
                                    <td>{{ $stats['utilisateurs_actifs'] ?? 'Non défini' }}</td>
                                    <td>{{ isset($stats['utilisateurs_actifs']) ? number_format($stats['utilisateurs_actifs']) : 'N/A' }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Debug des données -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Debug - Données brutes</h5>
                </div>
                <div class="card-body">
                    <pre>{{ print_r($stats ?? [], true) }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
console.log('Page de test des statistiques chargée');

/**
 * Charger les statistiques via AJAX
 */
function loadStats() {
    $.ajax({
        url: '/test/dashboard-stats',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                displayStats(response.data, response.formatted);
            } else {
                $('#stats-container').html('<div class="alert alert-danger">Erreur lors du chargement des statistiques</div>');
            }
        },
        error: function(xhr) {
            console.error('Erreur AJAX:', xhr);
            $('#stats-container').html('<div class="alert alert-danger">Erreur AJAX: ' + xhr.status + '</div>');
        }
    });
}

/**
 * Afficher les statistiques
 */
function displayStats(rawStats, formattedStats) {
    // Afficher les données brutes
    let html = '<div class="row">';
    html += '<div class="col-md-6">';
    html += '<h6>Données brutes:</h6>';
    html += '<pre>' + JSON.stringify(rawStats, null, 2) + '</pre>';
    html += '</div>';
    html += '<div class="col-md-6">';
    html += '<h6>Données formatées:</h6>';
    html += '<pre>' + JSON.stringify(formattedStats, null, 2) + '</pre>';
    html += '</div>';
    html += '</div>';
    
    $('#stats-container').html(html);
    
    // Générer les cartes comme dans le dashboard
    generateDashboardCards(formattedStats);
}

/**
 * Générer les cartes du dashboard
 */
function generateDashboardCards(stats) {
    const cards = [
        {
            title: 'Adhérents suspendus',
            value: stats.adherents_suspendus,
            icon: 'feather icon-user-x',
            color: 'bg-c-orange'
        },
        {
            title: 'Total cotisations payées',
            value: stats.montant_total_cotisations,
            icon: 'feather icon-dollar-sign',
            color: 'bg-c-purple'
        },
        {
            title: 'Cotisations en retard',
            value: stats.cotisations_en_retard,
            icon: 'feather icon-alert-triangle',
            color: 'bg-c-red'
        },
        {
            title: 'Adhérents actifs',
            value: stats.adherents_actifs,
            icon: 'feather icon-users',
            color: 'bg-c-blue'
        }
    ];
    
    let html = '';
    cards.forEach(card => {
        html += `
            <div class="col-xl-3 col-md-6 mb-3">
                <div class="card ${card.color} text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-8">
                                <h4 class="text-white f-w-600">${card.value}</h4>
                                <h6 class="text-white m-b-0">${card.title}</h6>
                            </div>
                            <div class="col-4 text-end">
                                <i class="${card.icon} f-28"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    });
    
    $('#dashboard-cards').html(html);
}

// Charger les statistiques au chargement de la page
$(document).ready(function() {
    loadStats();
});
</script>

<style>
.bg-c-blue { background: linear-gradient(45deg, #4099ff, #73b4ff); }
.bg-c-green { background: linear-gradient(45deg, #2ed8b6, #59e0c5); }
.bg-c-yellow { background: linear-gradient(45deg, #FFB64D, #ffcb80); }
.bg-c-red { background: linear-gradient(45deg, #FF5370, #ff869a); }
.bg-c-orange { background: linear-gradient(45deg, #FF8A65, #ffab91); }
.bg-c-purple { background: linear-gradient(45deg, #9C27B0, #ba68c8); }
.bg-c-pink { background: linear-gradient(45deg, #E91E63, #f06292); }
</style>
@endpush
