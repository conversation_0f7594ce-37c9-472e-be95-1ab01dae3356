<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Auth\Models\Permission;
use Modules\Auth\Models\Role;

class UpdatePermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Additional permissions for new modules
        $newPermissions = [
            // Cotisations permissions
            ['name' => 'view-cotisations', 'description' => 'Voir les cotisations', 'module' => 'cotisations'],
            ['name' => 'manage-cotisations', 'description' => 'Gérer les cotisations', 'module' => 'cotisations'],
            ['name' => 'generate-cotisations', 'description' => 'Générer les cotisations en masse', 'module' => 'cotisations'],
            ['name' => 'validate-cotisations', 'description' => 'Valider les cotisations', 'module' => 'cotisations'],
            
            // Pensions permissions
            ['name' => 'view-pensions', 'description' => 'Voir les dossiers de pension', 'module' => 'pensions'],
            ['name' => 'manage-pensions', 'description' => 'Gérer les dossiers de pension', 'module' => 'pensions'],
            ['name' => 'validate-pensions', 'description' => 'Valider les dossiers de pension', 'module' => 'pensions'],
            ['name' => 'liquidate-pensions', 'description' => 'Liquider les pensions', 'module' => 'pensions'],
            ['name' => 'pre-liquidation', 'description' => 'Effectuer la pré-liquidation', 'module' => 'pensions'],
            
            // Reports permissions
            ['name' => 'view-reports', 'description' => 'Voir les rapports', 'module' => 'reports'],
            ['name' => 'generate-reports', 'description' => 'Générer des rapports', 'module' => 'reports'],
            ['name' => 'export-reports', 'description' => 'Exporter les rapports', 'module' => 'reports'],
        ];

        // Create permissions
        foreach ($newPermissions as $permissionData) {
            Permission::firstOrCreate(
                ['name' => $permissionData['name']],
                $permissionData
            );
        }

        // Update role permissions
        $this->updateRolePermissions();
    }

    /**
     * Update role permissions
     */
    private function updateRolePermissions(): void
    {
        // Admin role - all permissions
        $adminRole = Role::where('name', 'admin')->first();
        if ($adminRole) {
            $allPermissions = Permission::all();
            $adminRole->permissions()->sync($allPermissions->pluck('id'));
        }

        // Gestionnaire role - most permissions except admin-specific ones
        $gestionnaireRole = Role::where('name', 'gestionnaire')->first();
        if ($gestionnaireRole) {
            $gestionnairePermissions = Permission::whereNotIn('name', [
                'manage-users',
                'manage-roles',
                'system-settings'
            ])->get();
            $gestionnaireRole->permissions()->sync($gestionnairePermissions->pluck('id'));
        }

        // Consultant role - view permissions only
        $consultantRole = Role::where('name', 'consultant')->first();
        if ($consultantRole) {
            $consultantPermissions = Permission::where('name', 'like', 'view-%')->get();
            $consultantRole->permissions()->sync($consultantPermissions->pluck('id'));
        }
    }
}
