<!DOCTYPE html>
<html lang="fr">
<head>
    <title>CRFM - Connexion</title>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Application de gestion des cotisations et de pré-liquidation de pension pour le CRFM">
    <meta name="keywords" content="CRFM, cotisations, pension, pré-liquidation, organisme public">
    <meta name="author" content="CRFM">
    <!-- Favicon icon -->
    <link rel="icon" href="{{ asset('assets/images/favicon.ico') }}" type="image/x-icon">
    <!-- Google font-->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600,800" rel="stylesheet">
    <!-- Required Framework -->
    <link rel="stylesheet" type="text/css" href="{{ asset('bower_components/bootstrap/dist/css/bootstrap.min.css') }}">
    <!-- themify-icons line icon -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/icon/themify-icons/themify-icons.css') }}">
    <!-- ico font -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/icon/icofont/css/icofont.css') }}">
    <!-- feather Awesome -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/icon/feather/css/feather.css') }}">
    <!-- Style.css -->
    <link rel="stylesheet" type="text/css" href="{{ asset('assets/css/style.css') }}">
    <style>
        .auth-wrapper {
            background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
            min-height: 100vh;
        }
        .auth-content {
            background: white;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .auth-form-light {
            padding: 40px;
        }
        .brand-logo {
            text-align: center;
            margin-bottom: 30px;
        }
        .brand-logo h2 {
            color: #1a237e;
            font-weight: 600;
        }
        .btn-primary {
            background: linear-gradient(135deg, #1a237e 0%, #283593 100%);
            border: none;
        }
        .btn-primary:hover {
            background: linear-gradient(135deg, #283593 0%, #1a237e 100%);
        }
    </style>
</head>

<body>
    <div class="auth-wrapper">
        <div class="auth-content">
            <div class="container">
                <div class="row justify-content-center">
                    <div class="col-md-6 col-lg-5">
                        <div class="auth-form-light">
                            <div class="brand-logo">
                                <h2>CRFM</h2>
                                <p class="text-muted">Gestion des Cotisations et Pensions</p>
                            </div>
                            
                            @if (session('success'))
                                <div class="alert alert-success">
                                    {{ session('success') }}
                                </div>
                            @endif

                            @if (session('error'))
                                <div class="alert alert-danger">
                                    {{ session('error') }}
                                </div>
                            @endif

                            @if (isset($errors) && $errors->any())
                                <div class="alert alert-danger">
                                    @foreach ($errors->all() as $error)
                                        <div>{{ $error }}</div>
                                    @endforeach
                                </div>
                            @endif

                            <form method="POST" action="{{ route('auth.login.post') }}">
                                @csrf
                                <div class="form-group">
                                    <label for="email">Adresse Email</label>
                                    <input type="email"
                                           class="form-control {{ (isset($errors) && $errors->has('email')) ? 'is-invalid' : '' }}"
                                           id="email"
                                           name="email"
                                           value="{{ old('email') }}"
                                           required
                                           autofocus>
                                    @if (isset($errors) && $errors->has('email'))
                                        <div class="invalid-feedback">{{ $errors->first('email') }}</div>
                                    @endif
                                </div>
                                
                                <div class="form-group">
                                    <label for="password">Mot de passe</label>
                                    <input type="password"
                                           class="form-control {{ (isset($errors) && $errors->has('password')) ? 'is-invalid' : '' }}"
                                           id="password"
                                           name="password"
                                           required>
                                    @if (isset($errors) && $errors->has('password'))
                                        <div class="invalid-feedback">{{ $errors->first('password') }}</div>
                                    @endif
                                </div>
                                
                                <div class="form-group">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="remember" name="remember">
                                        <label class="form-check-label" for="remember">
                                            Se souvenir de moi
                                        </label>
                                    </div>
                                </div>
                                
                                <div class="mt-3">
                                    <button type="submit" class="btn btn-primary btn-block">
                                        Se connecter
                                    </button>
                                </div>
                                
                                <div class="my-2 d-flex justify-content-between align-items-center">
                                    <a href="{{ route('auth.forgot-password') }}" class="auth-link text-black">
                                        Mot de passe oublié ?
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Required Js -->
    <script src="{{ asset('bower_components/jquery/dist/jquery.min.js') }}"></script>
    <script src="{{ asset('bower_components/bootstrap/dist/js/bootstrap.min.js') }}"></script>
</body>
</html>
