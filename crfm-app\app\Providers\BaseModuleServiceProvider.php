<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;

abstract class BaseModuleServiceProvider extends ServiceProvider
{
    /**
     * Module name
     */
    protected string $moduleName;

    /**
     * Module path
     */
    protected string $modulePath;

    public function __construct($app)
    {
        parent::__construct($app);
        $this->moduleName = $this->getModuleName();
        $this->modulePath = base_path("modules/{$this->moduleName}");
    }

    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerConfig();
        $this->registerServices();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootRoutes();
        $this->bootViews();
        $this->bootMigrations();
        $this->bootTranslations();
        $this->bootPublishing();
    }

    /**
     * Get module name
     */
    abstract protected function getModuleName(): string;

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Override in child classes
    }

    /**
     * Register module config
     */
    protected function registerConfig(): void
    {
        $configPath = $this->modulePath . '/config.php';
        if (File::exists($configPath)) {
            $this->mergeConfigFrom($configPath, strtolower($this->moduleName));
        }
    }

    /**
     * Boot module routes
     */
    protected function bootRoutes(): void
    {
        $routesPath = $this->modulePath . '/routes.php';
        if (File::exists($routesPath)) {
            $this->loadRoutesFrom($routesPath);
        }
    }

    /**
     * Boot module views
     */
    protected function bootViews(): void
    {
        $viewsPath = $this->modulePath . '/Views';
        if (File::exists($viewsPath)) {
            $this->loadViewsFrom($viewsPath, strtolower($this->moduleName));
        }
    }

    /**
     * Boot module migrations
     */
    protected function bootMigrations(): void
    {
        $migrationsPath = $this->modulePath . '/Migrations';
        if (File::exists($migrationsPath)) {
            $this->loadMigrationsFrom($migrationsPath);
        }
    }

    /**
     * Boot module translations
     */
    protected function bootTranslations(): void
    {
        $translationsPath = $this->modulePath . '/lang';
        if (File::exists($translationsPath)) {
            $this->loadTranslationsFrom($translationsPath, strtolower($this->moduleName));
        }
    }

    /**
     * Boot publishing
     */
    protected function bootPublishing(): void
    {
        if ($this->app->runningInConsole()) {
            $this->publishes([
                $this->modulePath . '/config.php' => config_path(strtolower($this->moduleName) . '.php'),
            ], $this->moduleName . '-config');

            $viewsPath = $this->modulePath . '/Views';
            if (File::exists($viewsPath)) {
                $this->publishes([
                    $viewsPath => resource_path('views/vendor/' . strtolower($this->moduleName)),
                ], $this->moduleName . '-views');
            }
        }
    }
}
