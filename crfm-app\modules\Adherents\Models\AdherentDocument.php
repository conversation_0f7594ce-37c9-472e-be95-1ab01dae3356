<?php

namespace Modules\Adherents\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class AdherentDocument extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'type_document',
        'nom_document',
        'chemin_fichier',
        'taille_fichier',
        'type_mime',
        'date_upload',
        'uploaded_by',
        'is_verified',
        'observations'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_upload' => 'datetime',
        'is_verified' => 'boolean',
        'taille_fichier' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Document type constants
     */
    const TYPE_CNI = 'cni';
    const TYPE_PASSEPORT = 'passeport';
    const TYPE_ACTE_NAISSANCE = 'acte_naissance';
    const TYPE_CERTIFICAT_TRAVAIL = 'certificat_travail';
    const TYPE_BULLETIN_SALAIRE = 'bulletin_salaire';
    const TYPE_PHOTO = 'photo';
    const TYPE_AUTRE = 'autre';

    /**
     * Get adherent that owns this document
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get user who uploaded this document
     */
    public function uploader(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'uploaded_by');
    }

    /**
     * Get formatted file size
     */
    public function getFormattedSizeAttribute(): string
    {
        $bytes = $this->taille_fichier;
        $units = ['B', 'KB', 'MB', 'GB'];
        
        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }
        
        return round($bytes, 2) . ' ' . $units[$i];
    }

    /**
     * Get document type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type_document) {
            self::TYPE_CNI => 'Carte Nationale d\'Identité',
            self::TYPE_PASSEPORT => 'Passeport',
            self::TYPE_ACTE_NAISSANCE => 'Acte de Naissance',
            self::TYPE_CERTIFICAT_TRAVAIL => 'Certificat de Travail',
            self::TYPE_BULLETIN_SALAIRE => 'Bulletin de Salaire',
            self::TYPE_PHOTO => 'Photo d\'Identité',
            self::TYPE_AUTRE => 'Autre Document',
            default => 'Document'
        };
    }

    /**
     * Check if document is an image
     */
    public function isImage(): bool
    {
        return str_starts_with($this->type_mime, 'image/');
    }

    /**
     * Check if document is a PDF
     */
    public function isPdf(): bool
    {
        return $this->type_mime === 'application/pdf';
    }

    /**
     * Scope for verified documents
     */
    public function scopeVerified($query)
    {
        return $query->where('is_verified', true);
    }

    /**
     * Scope by document type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type_document', $type);
    }
}
