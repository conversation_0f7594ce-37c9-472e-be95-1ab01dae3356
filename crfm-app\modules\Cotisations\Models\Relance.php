<?php

namespace Modules\Cotisations\Models;

use App\Models\BaseModel;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Relance extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'cotisation_id',
        'type_relance',
        'date_relance',
        'date_envoi',
        'canal_envoi',
        'message',
        'statut',
        'date_reponse',
        'observations',
        'created_by'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_relance' => 'date',
        'date_envoi' => 'datetime',
        'date_reponse' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Type constants
     */
    const TYPE_PREMIERE = 'premiere';
    const TYPE_DEUXIEME = 'deuxieme';
    const TYPE_DERNIERE = 'derniere';
    const TYPE_MISE_EN_DEMEURE = 'mise_en_demeure';

    /**
     * Canal constants
     */
    const CANAL_EMAIL = 'email';
    const CANAL_SMS = 'sms';
    const CANAL_COURRIER = 'courrier';
    const CANAL_TELEPHONE = 'telephone';

    /**
     * Status constants
     */
    const STATUS_PROGRAMMEE = 'programmee';
    const STATUS_ENVOYEE = 'envoyee';
    const STATUS_RECUE = 'recue';
    const STATUS_SANS_REPONSE = 'sans_reponse';

    /**
     * Get adherent that owns this relance
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get cotisation that owns this relance
     */
    public function cotisation(): BelongsTo
    {
        return $this->belongsTo(Cotisation::class);
    }

    /**
     * Get user who created this relance
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type_relance) {
            self::TYPE_PREMIERE => 'Première relance',
            self::TYPE_DEUXIEME => 'Deuxième relance',
            self::TYPE_DERNIERE => 'Dernière relance',
            self::TYPE_MISE_EN_DEMEURE => 'Mise en demeure',
            default => 'Relance'
        };
    }

    /**
     * Get canal label
     */
    public function getCanalLabelAttribute(): string
    {
        return match($this->canal_envoi) {
            self::CANAL_EMAIL => 'Email',
            self::CANAL_SMS => 'SMS',
            self::CANAL_COURRIER => 'Courrier postal',
            self::CANAL_TELEPHONE => 'Téléphone',
            default => 'Non défini'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_PROGRAMMEE => 'Programmée',
            self::STATUS_ENVOYEE => 'Envoyée',
            self::STATUS_RECUE => 'Reçue',
            self::STATUS_SANS_REPONSE => 'Sans réponse',
            default => 'Inconnu'
        };
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type_relance', $type);
    }

    /**
     * Scope by canal
     */
    public function scopeByCanal($query, string $canal)
    {
        return $query->where('canal_envoi', $canal);
    }

    /**
     * Scope for sent relances
     */
    public function scopeSent($query)
    {
        return $query->where('statut', self::STATUS_ENVOYEE);
    }

    /**
     * Mark as sent
     */
    public function markAsSent(): void
    {
        $this->update([
            'statut' => self::STATUS_ENVOYEE,
            'date_envoi' => now(),
        ]);
    }

    /**
     * Mark as received
     */
    public function markAsReceived(): void
    {
        $this->update([
            'statut' => self::STATUS_RECUE,
            'date_reponse' => now(),
        ]);
    }
}
