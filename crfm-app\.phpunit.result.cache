{"version": 1, "defects": {"Tests\\Unit\\NotificationServiceTest::it_can_create_cotisation_reminder": 8, "Tests\\Unit\\NotificationServiceTest::it_can_create_cotisation_overdue_notification": 8, "Tests\\Unit\\NotificationServiceTest::it_can_create_pension_step_notification": 8}, "times": {"Tests\\Unit\\NotificationServiceTest::it_can_create_a_notification": 0.025, "Tests\\Unit\\NotificationServiceTest::it_can_send_notification_to_user": 0.139, "Tests\\Unit\\NotificationServiceTest::it_can_get_unread_notifications_for_user": 0.014, "Tests\\Unit\\NotificationServiceTest::it_can_mark_all_notifications_as_read": 0.014, "Tests\\Unit\\NotificationServiceTest::it_can_create_cotisation_reminder": 0.025, "Tests\\Unit\\NotificationServiceTest::it_can_create_cotisation_overdue_notification": 0.009, "Tests\\Unit\\NotificationServiceTest::it_can_create_pension_step_notification": 0.01, "Tests\\Unit\\NotificationServiceTest::it_can_get_statistics": 0.015, "Tests\\Unit\\NotificationServiceTest::notification_has_correct_priority_color": 0.008, "Tests\\Unit\\NotificationServiceTest::notification_has_correct_type_icon": 0.008, "Tests\\Unit\\NotificationServiceTest::it_can_cleanup_expired_notifications": 0.01, "Tests\\Feature\\NotificationApiTest::it_can_get_unread_notifications": 0.116, "Tests\\Feature\\NotificationApiTest::it_can_mark_notification_as_read": 0.017, "Tests\\Feature\\NotificationApiTest::it_can_mark_all_notifications_as_read": 0.018, "Tests\\Feature\\NotificationApiTest::it_can_delete_notification": 0.013, "Tests\\Feature\\NotificationApiTest::it_can_send_notification": 0.124, "Tests\\Feature\\NotificationApiTest::it_can_get_statistics": 0.088, "Tests\\Feature\\NotificationApiTest::it_requires_authentication_for_api_routes": 0.014, "Tests\\Feature\\NotificationApiTest::it_prevents_access_to_other_users_notifications": 0.016, "Tests\\Feature\\NotificationApiTest::it_can_test_notification_system": 0.013}}