<?php

namespace Faker\Provider\en_HK;

class Address extends \Faker\Provider\Address
{
    protected static $country = ['Hong Kong'];

    protected static $syllables = [
        'A', '<PERSON>', 'Ak', 'Am', '<PERSON>', '<PERSON>', 'Ap', 'At', '<PERSON>',
        'Cha', '<PERSON><PERSON>', 'Cha<PERSON>', 'Cha<PERSON>', '<PERSON>', '<PERSON>', 'Chap', 'Chat', 'Chau',
        '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>',
        '<PERSON>', 'Chik', 'Chim', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>t', 'Chiu',
        '<PERSON>', '<PERSON>', 'Chok', 'Chong', 'Chou',
        'Chue', '<PERSON><PERSON>', '<PERSON>et', 'Chui', 'Chuk', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
        '<PERSON>', 'Ei',
        '<PERSON>a', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>u',
        'Hei', 'Hek', 'Heng', 'Heu', 'Heung',
        'Hik', 'Him', 'Hin', 'Hing', 'Hip', '<PERSON>', 'Hiu',
        'Ho', 'Hoi', 'Hok', 'Hon', 'Hong', '<PERSON>', 'Hou',
        'Huen', 'Huet', 'Hui', 'Huk', 'Hung',
        'Ka', 'Kai', 'Kak', 'Kam', 'Kan', 'Kang', 'Kap', 'Kat', 'Kau',
        'Ke', 'Kei', 'Kek', 'Keng', 'Keu', 'Keuk', 'Keung',
        'Kik', 'Kim', 'Kin', 'King', 'Kip', 'Kit', 'Kiu',
        'Ko', 'Koi', 'Kok', 'Kon', 'Kong', 'Kot', 'Kou',
        'Ku', 'Kuen', 'Kuet', 'Kui', 'Kuk', 'Kun', 'Kung', 'Kut',
        'Kwa', 'Kwai', 'Kwak', 'Kwan', 'Kwang', 'Kwat', 'Kwik', 'Kwing', 'Kwo', 'Kwok', 'Kwong',
        'La', 'Lai', 'Lak', 'Lam', 'Lan', 'Lang', 'Lap', 'Lat', 'Lau',
        'Le', 'Lei', 'Lek', 'Leng', 'Leuk', 'Leung',
        'Li', 'Lik', 'Lim', 'Lin', 'Ling', 'Lip', 'Lit', 'Liu',
        'Lo', 'Loi', 'Lok', 'Long', 'Lou',
        'Luen', 'Luet', 'Lui', 'Luk', 'Lun', 'Lung', 'Lut',
        'Ma', 'Mai', 'Mak', 'Man', 'Mang', 'Mat', 'Mau',
        'Me', 'Mei', 'Meng',
        'Mi', 'Mik', 'Min', 'Ming', 'Mit', 'Miu',
        'Mo', 'Mok', 'Mong', 'Mou',
        'Mui', 'Muk', 'Mun', 'Mung', 'Mut',
        'Na', 'Nai', 'Nam', 'Nan', 'Nang', 'Nap', 'Nat', 'Nau',
        'Ne', 'Nei', 'Neung',
        'Ng', 'Nga', 'Ngai', 'Ngak', 'Ngam', 'Ngan', 'Ngang', 'Ngap', 'Ngat', 'Ngau',
        'Ngit',
        'Ngo', 'Ngoi', 'Ngok', 'Ngon', 'Ngong', 'Ngou',
        'Ni', 'Nik', 'Nim', 'Nin', 'Ning', 'Nip', 'Niu',
        'No', 'Noi', 'Nok', 'Nong', 'Nou', 'Nuen',
        'Nui', 'Nuk', 'Nung', 'Nut',
        'O', 'Oi', 'Ok', 'On', 'Ong', 'Ou',
        'Pa', 'Pai', 'Pak', 'Pam', 'Pan', 'Pang', 'Pat', 'Pau',
        'Pe', 'Pei', 'Pek', 'Peng',
        'Pik', 'Pin', 'Ping', 'Pit', 'Piu',
        'Po', 'Poi', 'Pok', 'Pong', 'Pou',
        'Pui', 'Puk', 'Pun', 'Pung', 'Put',
        'Sa', 'Sai', 'Sak', 'Sam', 'San', 'Sang', 'Sap', 'Sat', 'Sau',
        'Se', 'Sei', 'Sek', 'Seng', 'Seuk', 'Seung',
        'Sha', 'Shai', 'Shak', 'Sham', 'Shan', 'Shang', 'Shap', 'Shat', 'Shau',
        'She', 'Shei', 'Shek', 'Sheng', 'Sheuk', 'Sheung',
        'Shi', 'Shik', 'Shim', 'Shin', 'Shing', 'Ship', 'Shit', 'Shiu',
        'Sho', 'Shoi', 'Shok', 'Shong', 'Shou',
        'Shue', 'Shuen', 'Shuet', 'Shui', 'Shuk', 'Shun', 'Shung', 'Shut',
        'Sik', 'Sim', 'Sin', 'Sing', 'Sip', 'Sit', 'Siu',
        'So', 'Soi', 'Sok', 'Song', 'Sou',
        'Sue', 'Suen', 'Suet', 'Sui', 'Suk', 'Sun', 'Sung', 'Sut',
        'Sze',
        'Ta', 'Tai', 'Tak', 'Tam', 'Tan', 'Tang', 'Tap', 'Tat', 'Tau',
        'Te', 'Tei', 'Tek', 'Teng', 'Teu', 'Teuk',
        'Tik', 'Tim', 'Tin', 'Ting', 'Tip', 'Tit', 'Tiu',
        'To', 'Toi', 'Tok', 'Tong', 'Tou',
        'Tsa', 'Tsai', 'Tsak', 'Tsam', 'Tsan', 'Tsang', 'Tsap', 'Tsat', 'Tsau',
        'Tse', 'Tsek', 'Tseng', 'Tseuk', 'Tseung',
        'Tsik', 'Tsim', 'Tsin', 'Tsing', 'Tsip', 'Tsit', 'Tsiu',
        'Tso', 'Tsoi', 'Tsok', 'Tsong', 'Tsou',
        'Tsue', 'Tsuen', 'Tsuet', 'Tsui', 'Tsuk', 'Tsun', 'Tsung', 'Tsut',
        'Tsz',
        'Tuen', 'Tuet', 'Tui', 'Tuk', 'Tun', 'Tung', 'Tut',
        'Uk', 'Ung',
        'Wa', 'Wai', 'Wak', 'Wan', 'Wang', 'Wat',
        'Wik', 'Wing',
        'Wo', 'Wok', 'Wong', 'Wu',
        'Wui', 'Wun', 'Wut', 'Ya',
        'Yai', 'Yak', 'Yam', 'Yan', 'Yap', 'Yat', 'Yau',
        'Ye', 'Yeng', 'Yeuk', 'Yeung', 'Yi',
        'Yik', 'Yim', 'Yin', 'Ying', 'Yip', 'Yit', 'Yiu',
        'Yo',
        'Yue', 'Yuen', 'Yuet', 'Yui', 'Yuk', 'Yun', 'Yung',
    ];

    protected static $streetAddressFormats = [
        '{{buildingNumber}} {{streetName}}',
        '{{buildingNumber}} {{village}}',
        'Block {{buildingNumber}}, {{estate}}',
    ];

    protected static $addressFormats = [
        "{{streetAddress}}\n{{town}}\n{{city}}",
    ];

    protected static $villageNameFormats = [
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{villageSuffix}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{town}}',
        '{{town}} {{villageSuffix}}',
    ];

    protected static $estateNameFormats = [
        '{{syllable}} {{syllable}} {{estateSuffix}}',
        '{{syllable}} {{syllable}} {{estateSuffix}}',
        '{{syllable}} {{syllable}} {{estateSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{estateSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{estateSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{estateSuffix}}',
        '{{town}} {{estateSuffix}}',
    ];

    protected static $villageSuffixes = ['Village', 'Tsuen', 'San Tsuen', 'New Village', 'Wai'];

    protected static $estateSuffixes = ['Estate', 'Court'];

    protected static $streetNameFormats = [
        '{{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{direction}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{syllable}} {{direction}} {{streetSuffix}}',
        '{{syllable}} {{syllable}} {{streetSuffix}} {{direction}}',
        '{{syllable}} {{syllable}} {{syllable}} {{streetSuffix}} {{direction}}',
        '{{englishStreetName}} {{streetSuffix}}',
        '{{englishStreetName}} {{streetSuffix}} {{direction}}',
    ];

    protected static $englishStreetNames = [
        "King's", "Queen's", 'Nathan', 'Robinson', 'Kingston', 'Irving', 'Bonham', 'Salisbury',
        'Tonkin', 'Nanking', 'Peking', 'Canton', 'Amoy', 'Shanghai',
    ];

    protected static $streetSuffix = [
        'Road', 'Road', 'Road', 'Street', 'Street', 'Street', 'Lane',
        'Circuit', 'Avenue', 'Path', 'Square',
    ];

    protected static $directions = ['North', 'East', 'South', 'West'];

    protected static $cities = ['Hong Kong', 'Kowloon', 'New Territories'];

    protected static $towns = [
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        '{{syllable}} {{syllable}} {{syllable}}',
        'Aberdeen',
        'Stanley',
        'Victoria',
    ];

    public function city()
    {
        return static::randomElement(static::$cities);
    }

    public function town()
    {
        return $this->generator->parse(static::randomElement(static::$towns));
    }

    public function syllable()
    {
        return static::randomElement(static::$syllables);
    }

    public function direction()
    {
        return static::randomElement(static::$directions);
    }

    public function englishStreetName()
    {
        return static::randomElement(static::$englishStreetNames);
    }

    public function villageSuffix()
    {
        return static::randomElement(static::$villageSuffixes);
    }

    public function estateSuffix()
    {
        return static::randomElement(static::$estateSuffixes);
    }

    public function village()
    {
        return $this->generator->parse(static::randomElement(static::$villageNameFormats));
    }

    public function estate()
    {
        return $this->generator->parse(static::randomElement(static::$estateNameFormats));
    }
}
