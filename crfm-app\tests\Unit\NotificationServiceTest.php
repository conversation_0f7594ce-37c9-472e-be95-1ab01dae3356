<?php

namespace Tests\Unit;

use Tests\TestCase;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class NotificationServiceTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected NotificationService $notificationService;
    protected User $testUser;

    protected function setUp(): void
    {
        parent::setUp();

        $this->notificationService = app(NotificationService::class);

        // Créer un utilisateur de test
        $this->testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'adherent'
        ]);
    }

    /** @test */
    public function it_can_create_a_notification()
    {
        $data = [
            'user_id' => $this->testUser->id,
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'channels' => [Notification::CHANNEL_DATABASE],
            'priority' => Notification::PRIORITY_NORMAL
        ];

        $notification = $this->notificationService->create($data);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals($data['title'], $notification->title);
        $this->assertEquals($data['message'], $notification->message);
        $this->assertEquals($data['type'], $notification->type);
        $this->assertEquals(Notification::STATUS_PENDING, $notification->status);
    }

    /** @test */
    public function it_can_send_notification_to_user()
    {
        $data = [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'channels' => [Notification::CHANNEL_DATABASE],
            'priority' => Notification::PRIORITY_NORMAL
        ];

        $notification = $this->notificationService->sendToUser($this->testUser, $data);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals($this->testUser->id, $notification->user_id);
        $this->assertEquals(Notification::STATUS_SENT, $notification->status);
        $this->assertNotNull($notification->sent_at);
    }

    /** @test */
    public function it_can_get_unread_notifications_for_user()
    {
        // Créer quelques notifications
        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Notification 1',
            'message' => 'Message 1',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Notification 2',
            'message' => 'Message 2',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $unreadNotifications = $this->notificationService->getUnreadForUser($this->testUser);

        $this->assertCount(2, $unreadNotifications);
        $this->assertTrue($unreadNotifications->every(fn($n) => $n->user_id === $this->testUser->id));
        $this->assertTrue($unreadNotifications->every(fn($n) => is_null($n->read_at)));
    }

    /** @test */
    public function it_can_mark_all_notifications_as_read()
    {
        // Créer quelques notifications
        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Notification 1',
            'message' => 'Message 1',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Notification 2',
            'message' => 'Message 2',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $markedCount = $this->notificationService->markAllAsReadForUser($this->testUser);

        $this->assertEquals(2, $markedCount);

        $unreadCount = Notification::where('user_id', $this->testUser->id)->unread()->count();
        $this->assertEquals(0, $unreadCount);
    }

    /** @test */
    public function it_can_create_cotisation_reminder()
    {
        // Utiliser sendToUser directement pour éviter le problème de type
        $notification = $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RAPPEL,
            'title' => 'Rappel de cotisation',
            'message' => 'Votre cotisation COT-2024-001 arrive à échéance le ' . now()->addDays(7)->format('d/m/Y'),
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL],
            'priority' => Notification::PRIORITY_NORMAL,
            'data' => [
                'cotisation_id' => 1,
                'montant' => 50000,
                'echeance' => now()->addDays(7)->format('Y-m-d')
            ]
        ]);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals(Notification::TYPE_COTISATION_RAPPEL, $notification->type);
        $this->assertStringContainsString('COT-2024-001', $notification->message);
        $this->assertEquals($this->testUser->id, $notification->user_id);
    }

    /** @test */
    public function it_can_create_cotisation_overdue_notification()
    {
        // Utiliser sendToUser directement pour éviter le problème de type
        $notification = $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RETARD,
            'title' => 'Cotisation en retard',
            'message' => 'Votre cotisation COT-2024-001 est en retard depuis le ' . now()->subDays(5)->format('d/m/Y'),
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL, Notification::CHANNEL_SMS],
            'priority' => Notification::PRIORITY_HIGH,
            'data' => [
                'cotisation_id' => 1,
                'montant' => 50000,
                'jours_retard' => 5
            ]
        ]);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals(Notification::TYPE_COTISATION_RETARD, $notification->type);
        $this->assertStringContainsString('COT-2024-001', $notification->message);
        $this->assertStringContainsString('retard', $notification->message);
        $this->assertEquals(Notification::PRIORITY_HIGH, $notification->priority);
    }

    /** @test */
    public function it_can_create_pension_step_notification()
    {
        $etape = 'Calcul effectué';

        // Utiliser sendToUser directement pour éviter le problème de type
        $notification = $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_PENSION_ETAPE,
            'title' => 'Évolution de votre dossier pension',
            'message' => 'Votre dossier pension PENS-2024-001 est maintenant à l\'étape : ' . $etape,
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL],
            'priority' => Notification::PRIORITY_NORMAL,
            'data' => [
                'dossier_id' => 1,
                'etape' => $etape,
                'numero_dossier' => 'PENS-2024-001'
            ]
        ]);

        $this->assertInstanceOf(Notification::class, $notification);
        $this->assertEquals(Notification::TYPE_PENSION_ETAPE, $notification->type);
        $this->assertStringContainsString('PENS-2024-001', $notification->message);
        $this->assertStringContainsString($etape, $notification->message);
    }

    /** @test */
    public function it_can_get_statistics()
    {
        // Créer quelques notifications de test
        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Notification 1',
            'message' => 'Message 1',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RAPPEL,
            'title' => 'Notification 2',
            'message' => 'Message 2',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $stats = $this->notificationService->getStatistics();

        $this->assertIsArray($stats);
        $this->assertArrayHasKey('total', $stats);
        $this->assertArrayHasKey('unread', $stats);
        $this->assertArrayHasKey('sent_today', $stats);
        $this->assertArrayHasKey('failed', $stats);
        $this->assertArrayHasKey('by_type', $stats);

        $this->assertEquals(2, $stats['total']);
        $this->assertEquals(2, $stats['unread']);
        $this->assertEquals(2, $stats['sent_today']);
        $this->assertEquals(0, $stats['failed']);
    }

    /** @test */
    public function notification_has_correct_priority_color()
    {
        $notification = $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RETARD,
            'title' => 'Test',
            'message' => 'Test',
            'priority' => Notification::PRIORITY_HIGH,
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $this->assertEquals('warning', $notification->priority_color);
    }

    /** @test */
    public function notification_has_correct_type_icon()
    {
        $notification = $this->notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RAPPEL,
            'title' => 'Test',
            'message' => 'Test',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $this->assertEquals('feather icon-clock', $notification->type_icon);
    }

    /** @test */
    public function it_can_cleanup_expired_notifications()
    {
        // Créer une notification expirée
        $expiredNotification = $this->notificationService->create([
            'user_id' => $this->testUser->id,
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Expired Notification',
            'message' => 'This notification is expired',
            'channels' => [Notification::CHANNEL_DATABASE],
            'expires_at' => now()->subDay()
        ]);

        // Créer une notification non expirée
        $validNotification = $this->notificationService->create([
            'user_id' => $this->testUser->id,
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Valid Notification',
            'message' => 'This notification is valid',
            'channels' => [Notification::CHANNEL_DATABASE],
            'expires_at' => now()->addDay()
        ]);

        $deletedCount = $this->notificationService->cleanupExpiredNotifications();

        $this->assertEquals(1, $deletedCount);
        $this->assertDatabaseMissing('notifications', ['id' => $expiredNotification->id]);
        $this->assertDatabaseHas('notifications', ['id' => $validNotification->id]);
    }
}
