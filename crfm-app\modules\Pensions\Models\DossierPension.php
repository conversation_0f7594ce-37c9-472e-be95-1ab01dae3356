<?php

namespace Modules\Pensions\Models;

use App\Models\BaseModel;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class DossierPension extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'adherent_id',
        'numero_dossier',
        'type_pension',
        'date_demande',
        'date_depart_retraite',
        'date_liquidation',
        'statut',
        'montant_pension_calcule',
        'montant_pension_liquide',
        'duree_cotisation_mois',
        'salaire_reference',
        'taux_liquidation',
        'observations',
        'created_by',
        'validated_by',
        'validated_at',
        'liquidated_by',
        'liquidated_at'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_demande' => 'date',
        'date_depart_retraite' => 'date',
        'date_liquidation' => 'date',
        'montant_pension_calcule' => 'decimal:2',
        'montant_pension_liquide' => 'decimal:2',
        'salaire_reference' => 'decimal:2',
        'taux_liquidation' => 'decimal:4',
        'duree_cotisation_mois' => 'integer',
        'validated_at' => 'datetime',
        'liquidated_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Searchable fields
     */
    protected array $searchable = [
        'numero_dossier',
        'type_pension'
    ];

    /**
     * Type constants
     */
    const TYPE_VIEILLESSE = 'vieillesse';
    const TYPE_INVALIDITE = 'invalidite';
    const TYPE_SURVIVANT = 'survivant';
    const TYPE_ANTICIPEE = 'anticipee';

    /**
     * Status constants
     */
    const STATUS_EN_COURS = 'en_cours';
    const STATUS_VALIDE = 'valide';
    const STATUS_LIQUIDE = 'liquide';
    const STATUS_REJETE = 'rejete';
    const STATUS_SUSPENDU = 'suspendu';

    /**
     * Get adherent that owns this dossier
     */
    public function adherent(): BelongsTo
    {
        return $this->belongsTo(Adherent::class);
    }

    /**
     * Get user who created this dossier
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'created_by');
    }

    /**
     * Get user who validated this dossier
     */
    public function validator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'validated_by');
    }

    /**
     * Get user who liquidated this dossier
     */
    public function liquidator(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'liquidated_by');
    }

    /**
     * Get pre-liquidation for this dossier
     */
    public function preLiquidation(): HasOne
    {
        return $this->hasOne(PreLiquidation::class);
    }

    /**
     * Get documents for this dossier
     */
    public function documents(): HasMany
    {
        return $this->hasMany(DossierDocument::class);
    }

    /**
     * Get workflow steps for this dossier
     */
    public function workflowSteps(): HasMany
    {
        return $this->hasMany(WorkflowStep::class);
    }

    /**
     * Get type label
     */
    public function getTypeLabelAttribute(): string
    {
        return match($this->type_pension) {
            self::TYPE_VIEILLESSE => 'Pension de vieillesse',
            self::TYPE_INVALIDITE => 'Pension d\'invalidité',
            self::TYPE_SURVIVANT => 'Pension de survivant',
            self::TYPE_ANTICIPEE => 'Pension anticipée',
            default => 'Type inconnu'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_COURS => 'En cours',
            self::STATUS_VALIDE => 'Validé',
            self::STATUS_LIQUIDE => 'Liquidé',
            self::STATUS_REJETE => 'Rejeté',
            self::STATUS_SUSPENDU => 'Suspendu',
            default => 'Statut inconnu'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_COURS => 'warning',
            self::STATUS_VALIDE => 'info',
            self::STATUS_LIQUIDE => 'success',
            self::STATUS_REJETE => 'danger',
            self::STATUS_SUSPENDU => 'secondary',
            default => 'secondary'
        };
    }

    /**
     * Get formatted pension amount
     */
    public function getFormattedPensionAttribute(): string
    {
        $amount = $this->montant_pension_liquide ?? $this->montant_pension_calcule;
        return number_format($amount, 0, ',', ' ') . ' €';
    }

    /**
     * Get years of contribution
     */
    public function getYearsOfContributionAttribute(): float
    {
        return round($this->duree_cotisation_mois / 12, 2);
    }

    /**
     * Check if dossier is liquidated
     */
    public function isLiquidated(): bool
    {
        return $this->statut === self::STATUS_LIQUIDE;
    }

    /**
     * Check if dossier is validated
     */
    public function isValidated(): bool
    {
        return in_array($this->statut, [self::STATUS_VALIDE, self::STATUS_LIQUIDE]);
    }

    /**
     * Scope for liquidated dossiers
     */
    public function scopeLiquidated($query)
    {
        return $query->where('statut', self::STATUS_LIQUIDE);
    }

    /**
     * Scope for validated dossiers
     */
    public function scopeValidated($query)
    {
        return $query->where('statut', self::STATUS_VALIDE);
    }

    /**
     * Scope for pending dossiers
     */
    public function scopePending($query)
    {
        return $query->where('statut', self::STATUS_EN_COURS);
    }

    /**
     * Scope by type
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type_pension', $type);
    }

    /**
     * Scope by year
     */
    public function scopeByYear($query, int $year)
    {
        return $query->whereYear('date_demande', $year);
    }

    /**
     * Calculate pension amount based on salary and contribution period
     */
    public function calculatePensionAmount(): void
    {
        // Basic pension calculation formula
        // This is a simplified version - real calculation would be more complex
        
        $yearsOfContribution = $this->years_of_contribution;
        $referenceRate = 0.02; // 2% per year of contribution
        
        // Maximum rate is usually capped (e.g., 75%)
        $maxRate = 0.75;
        $calculatedRate = min($yearsOfContribution * $referenceRate, $maxRate);
        
        $this->taux_liquidation = $calculatedRate;
        $this->montant_pension_calcule = $this->salaire_reference * $calculatedRate;
    }

    /**
     * Generate next dossier number
     */
    public static function generateNextNumero(): string
    {
        $year = date('Y');
        
        $lastDossier = static::where('numero_dossier', 'LIKE', "PEN{$year}%")
                            ->orderBy('numero_dossier', 'desc')
                            ->first();

        if ($lastDossier) {
            $lastNumber = (int) substr($lastDossier->numero_dossier, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return "PEN{$year}" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Mark as validated
     */
    public function markAsValidated(int $validatedBy): void
    {
        $this->update([
            'statut' => self::STATUS_VALIDE,
            'validated_by' => $validatedBy,
            'validated_at' => now(),
        ]);
    }

    /**
     * Mark as liquidated
     */
    public function markAsLiquidated(int $liquidatedBy, float $finalAmount = null): void
    {
        $this->update([
            'statut' => self::STATUS_LIQUIDE,
            'montant_pension_liquide' => $finalAmount ?? $this->montant_pension_calcule,
            'date_liquidation' => now(),
            'liquidated_by' => $liquidatedBy,
            'liquidated_at' => now(),
        ]);
    }
}
