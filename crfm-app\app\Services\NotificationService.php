<?php

namespace App\Services;

use App\Models\Notification;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Collection;

class NotificationService
{
    /**
     * Créer une nouvelle notification
     */
    public function create(array $data): Notification
    {
        return Notification::create([
            'user_id' => $data['user_id'],
            'notifiable_type' => $data['notifiable_type'] ?? null,
            'notifiable_id' => $data['notifiable_id'] ?? null,
            'type' => $data['type'],
            'title' => $data['title'],
            'message' => $data['message'],
            'data' => $data['data'] ?? [],
            'channels' => $data['channels'] ?? [Notification::CHANNEL_DATABASE],
            'priority' => $data['priority'] ?? Notification::PRIORITY_NORMAL,
            'status' => Notification::STATUS_PENDING,
            'scheduled_at' => $data['scheduled_at'] ?? now(),
            'expires_at' => $data['expires_at'] ?? null,
        ]);
    }

    /**
     * Envoyer une notification à un utilisateur
     */
    public function sendToUser(User $user, array $data): Notification
    {
        $data['user_id'] = $user->id;
        $notification = $this->create($data);

        $this->processNotification($notification);

        return $notification;
    }

    /**
     * Envoyer une notification à plusieurs utilisateurs
     */
    public function sendToUsers(Collection $users, array $data): Collection
    {
        $notifications = collect();

        foreach ($users as $user) {
            $notifications->push($this->sendToUser($user, $data));
        }

        return $notifications;
    }

    /**
     * Envoyer une notification liée à un modèle
     */
    public function sendForModel(Model $model, array $data): Notification
    {
        $data['notifiable_type'] = get_class($model);
        $data['notifiable_id'] = $model->id;

        $user = $data['user'];
        unset($data['user']); // Retirer 'user' du tableau data

        return $this->sendToUser($user, $data);
    }

    /**
     * Traiter une notification selon ses canaux
     */
    protected function processNotification(Notification $notification): void
    {
        $channels = $notification->channels;
        $success = true;

        foreach ($channels as $channel) {
            try {
                switch ($channel) {
                    case Notification::CHANNEL_EMAIL:
                        $this->sendEmail($notification);
                        break;

                    case Notification::CHANNEL_SMS:
                        $this->sendSMS($notification);
                        break;

                    case Notification::CHANNEL_PUSH:
                        $this->sendPush($notification);
                        break;

                    case Notification::CHANNEL_DATABASE:
                        // Déjà stocké en base
                        break;
                }
            } catch (\Exception $e) {
                Log::error("Erreur envoi notification {$notification->id} via {$channel}: " . $e->getMessage());
                $success = false;
            }
        }

        $notification->update([
            'status' => $success ? Notification::STATUS_SENT : Notification::STATUS_FAILED,
            'sent_at' => now()
        ]);
    }

    /**
     * Envoyer par email
     */
    protected function sendEmail(Notification $notification): void
    {
        if (!$notification->user->email) {
            throw new \Exception('Utilisateur sans email');
        }

        // TODO: Implémenter l'envoi d'email avec un template
        // Mail::to($notification->user->email)->send(new NotificationMail($notification));

        Log::info("Email envoyé à {$notification->user->email} pour notification {$notification->id}");
    }

    /**
     * Envoyer par SMS
     */
    protected function sendSMS(Notification $notification): void
    {
        if (!$notification->user->telephone) {
            throw new \Exception('Utilisateur sans téléphone');
        }

        // TODO: Intégrer un service SMS (Twilio, etc.)
        Log::info("SMS envoyé à {$notification->user->telephone} pour notification {$notification->id}");
    }

    /**
     * Envoyer notification push
     */
    protected function sendPush(Notification $notification): void
    {
        // TODO: Implémenter les notifications push
        Log::info("Push notification envoyée pour notification {$notification->id}");
    }

    /**
     * Créer une notification de rappel de cotisation
     */
    public function createCotisationReminder(User $user, $cotisation): Notification
    {
        return $this->sendForModel($cotisation, [
            'user' => $user,
            'type' => Notification::TYPE_COTISATION_RAPPEL,
            'title' => 'Rappel de cotisation',
            'message' => "Votre cotisation {$cotisation->numero_cotisation} arrive à échéance le {$cotisation->date_echeance->format('d/m/Y')}",
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL],
            'priority' => Notification::PRIORITY_NORMAL,
            'data' => [
                'cotisation_id' => $cotisation->id,
                'montant' => $cotisation->montant_cotisation,
                'echeance' => $cotisation->date_echeance->format('Y-m-d')
            ]
        ]);
    }

    /**
     * Créer une notification de cotisation en retard
     */
    public function createCotisationOverdue(User $user, $cotisation): Notification
    {
        return $this->sendForModel($cotisation, [
            'user' => $user,
            'type' => Notification::TYPE_COTISATION_RETARD,
            'title' => 'Cotisation en retard',
            'message' => "Votre cotisation {$cotisation->numero_cotisation} est en retard depuis le {$cotisation->date_echeance->format('d/m/Y')}",
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL, Notification::CHANNEL_SMS],
            'priority' => Notification::PRIORITY_HIGH,
            'data' => [
                'cotisation_id' => $cotisation->id,
                'montant' => $cotisation->montant_cotisation,
                'jours_retard' => $cotisation->date_echeance->diffInDays(now())
            ]
        ]);
    }

    /**
     * Créer une notification d'étape de pension
     */
    public function createPensionStepNotification(User $user, $dossier, string $etape): Notification
    {
        return $this->sendForModel($dossier, [
            'user' => $user,
            'type' => Notification::TYPE_PENSION_ETAPE,
            'title' => 'Évolution de votre dossier pension',
            'message' => "Votre dossier pension {$dossier->numero_dossier} est maintenant à l'étape : {$etape}",
            'channels' => [Notification::CHANNEL_DATABASE, Notification::CHANNEL_EMAIL],
            'priority' => Notification::PRIORITY_NORMAL,
            'data' => [
                'dossier_id' => $dossier->id,
                'etape' => $etape,
                'numero_dossier' => $dossier->numero_dossier
            ]
        ]);
    }

    /**
     * Obtenir les notifications non lues d'un utilisateur
     */
    public function getUnreadForUser(User $user): Collection
    {
        return Notification::where('user_id', $user->id)
            ->unread()
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Marquer toutes les notifications d'un utilisateur comme lues
     */
    public function markAllAsReadForUser(User $user): int
    {
        return Notification::where('user_id', $user->id)
            ->unread()
            ->update(['read_at' => now()]);
    }

    /**
     * Supprimer les notifications expirées
     */
    public function cleanupExpiredNotifications(): int
    {
        return Notification::whereNotNull('expires_at')
            ->where('expires_at', '<', now())
            ->delete();
    }

    /**
     * Obtenir les statistiques des notifications
     */
    public function getStatistics(): array
    {
        return [
            'total' => Notification::count(),
            'unread' => Notification::unread()->count(),
            'sent_today' => Notification::sent()->whereDate('sent_at', today())->count(),
            'failed' => Notification::where('status', Notification::STATUS_FAILED)->count(),
            'by_type' => Notification::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type')
                ->toArray()
        ];
    }
}
