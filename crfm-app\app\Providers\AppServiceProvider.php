<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        //
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Ensure $errors is always available in views
        view()->composer('*', function ($view) {
            if (!$view->offsetExists('errors')) {
                $view->with('errors', session()->get('errors', new \Illuminate\Support\MessageBag()));
            }
        });
    }
}
