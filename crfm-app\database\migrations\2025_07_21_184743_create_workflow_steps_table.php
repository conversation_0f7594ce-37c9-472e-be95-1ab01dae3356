<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('workflow_steps', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('dossier_pension_id')->constrained()->onDelete('cascade');

            // Définition de l'étape
            $table->enum('step_name', [
                'reception', 'verification_documents', 'calcul_droits',
                'validation_technique', 'validation_hierarchique',
                'pre_liquidation', 'liquidation', 'mise_en_paiement'
            ]);
            $table->integer('step_order');

            // Statut et dates
            $table->enum('statut', ['en_attente', 'en_cours', 'termine', 'bloque', 'annule'])->default('en_attente');
            $table->timestamp('date_debut')->nullable();
            $table->timestamp('date_fin')->nullable();

            // Assignation et completion
            $table->foreignId('assigned_to')->nullable()->constrained('users')->onDelete('set null');
            $table->foreignId('completed_by')->nullable()->constrained('users')->onDelete('set null');

            // Configuration
            $table->json('required_documents')->nullable(); // Documents requis pour cette étape
            $table->text('observations')->nullable();
            $table->foreignId('next_step_id')->nullable()->constrained('workflow_steps')->onDelete('set null');

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['dossier_pension_id', 'step_order']);
            $table->index(['statut']);
            $table->index(['assigned_to']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('workflow_steps');
    }
};
