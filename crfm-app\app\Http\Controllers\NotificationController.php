<?php

namespace App\Http\Controllers;

use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;

class NotificationController extends BaseController
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Afficher la liste des notifications
     */
    public function index(Request $request): View
    {
        $user = auth()->user();
        
        $notifications = Notification::where('user_id', $user->id)
            ->when($request->type, fn($q) => $q->ofType($request->type))
            ->when($request->status === 'unread', fn($q) => $q->unread())
            ->when($request->status === 'read', fn($q) => $q->read())
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        $statistics = [
            'total' => Notification::where('user_id', $user->id)->count(),
            'unread' => Notification::where('user_id', $user->id)->unread()->count(),
            'read' => Notification::where('user_id', $user->id)->read()->count(),
        ];

        return view('notifications.index', compact('notifications', 'statistics'));
    }

    /**
     * Obtenir les notifications non lues (AJAX)
     */
    public function getUnread(): JsonResponse
    {
        $user = auth()->user();
        $notifications = $this->notificationService->getUnreadForUser($user);
        
        return $this->successResponse('Notifications récupérées', [
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'title' => $notification->title,
                    'message' => $notification->message,
                    'type' => $notification->type,
                    'type_icon' => $notification->type_icon,
                    'priority_color' => $notification->priority_color,
                    'time_ago' => $notification->time_ago,
                    'created_at' => $notification->created_at->format('d/m/Y H:i')
                ];
            }),
            'count' => $notifications->count()
        ]);
    }

    /**
     * Marquer une notification comme lue
     */
    public function markAsRead(Notification $notification): JsonResponse
    {
        // Vérifier que la notification appartient à l'utilisateur connecté
        if ($notification->user_id !== auth()->id()) {
            return $this->errorResponse('Notification non trouvée', [], 404);
        }

        $notification->markAsRead();

        return $this->successResponse('Notification marquée comme lue');
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = auth()->user();
        $count = $this->notificationService->markAllAsReadForUser($user);

        return $this->successResponse("$count notifications marquées comme lues");
    }

    /**
     * Supprimer une notification
     */
    public function destroy(Notification $notification): JsonResponse
    {
        // Vérifier que la notification appartient à l'utilisateur connecté
        if ($notification->user_id !== auth()->id()) {
            return $this->errorResponse('Notification non trouvée', [], 404);
        }

        $notification->delete();

        return $this->successResponse('Notification supprimée');
    }

    /**
     * Envoyer une notification personnalisée
     */
    public function send(Request $request): JsonResponse
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'type' => 'required|string',
            'title' => 'required|string|max:255',
            'message' => 'required|string',
            'channels' => 'array',
            'channels.*' => 'in:email,sms,push,database',
            'priority' => 'in:low,normal,high,urgent',
            'scheduled_at' => 'nullable|date|after:now',
            'expires_at' => 'nullable|date|after:scheduled_at'
        ]);

        try {
            $user = \App\Models\User::findOrFail($request->user_id);
            
            $notification = $this->notificationService->sendToUser($user, [
                'type' => $request->type,
                'title' => $request->title,
                'message' => $request->message,
                'channels' => $request->channels ?? ['database'],
                'priority' => $request->priority ?? 'normal',
                'scheduled_at' => $request->scheduled_at,
                'expires_at' => $request->expires_at,
                'data' => $request->data ?? []
            ]);

            return $this->successResponse('Notification envoyée avec succès', [
                'notification_id' => $notification->id
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'envoi : ' . $e->getMessage());
        }
    }

    /**
     * Envoyer une notification à un adhérent
     */
    public function sendToAdherent(Request $request): JsonResponse
    {
        $request->validate([
            'adherent_id' => 'required|exists:adherents,id',
            'type' => 'required|string',
            'message' => 'required|string',
            'send_email' => 'boolean',
            'send_sms' => 'boolean'
        ]);

        try {
            $adherent = \Modules\Adherents\Models\Adherent::findOrFail($request->adherent_id);
            
            // Déterminer les canaux d'envoi
            $channels = ['database'];
            if ($request->send_email && $adherent->email) {
                $channels[] = 'email';
            }
            if ($request->send_sms && $adherent->telephone) {
                $channels[] = 'sms';
            }

            // Créer un utilisateur temporaire pour l'adhérent si nécessaire
            $user = \App\Models\User::where('email', $adherent->email)->first();
            if (!$user && $adherent->email) {
                $user = \App\Models\User::create([
                    'name' => $adherent->full_name,
                    'email' => $adherent->email,
                    'password' => bcrypt('temporary'),
                    'role' => 'adherent'
                ]);
            }

            if (!$user) {
                return $this->errorResponse('Impossible de créer une notification pour cet adhérent');
            }

            $titles = [
                'cotisation_rappel' => 'Rappel de cotisation',
                'document_manquant' => 'Document manquant',
                'rdv_convocation' => 'Convocation',
                'info_generale' => 'Information'
            ];

            $notification = $this->notificationService->sendForModel($adherent, [
                'user' => $user,
                'type' => $request->type,
                'title' => $titles[$request->type] ?? 'Notification',
                'message' => $request->message,
                'channels' => $channels,
                'priority' => 'normal',
                'data' => [
                    'adherent_id' => $adherent->id,
                    'adherent_name' => $adherent->full_name
                ]
            ]);

            return $this->successResponse('Notification envoyée à l\'adhérent', [
                'notification_id' => $notification->id,
                'channels_used' => $channels
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'envoi : ' . $e->getMessage());
        }
    }

    /**
     * Obtenir les statistiques des notifications
     */
    public function getStatistics(): JsonResponse
    {
        $stats = $this->notificationService->getStatistics();
        
        return $this->successResponse('Statistiques récupérées', $stats);
    }

    /**
     * Nettoyer les notifications expirées
     */
    public function cleanup(): JsonResponse
    {
        $deleted = $this->notificationService->cleanupExpiredNotifications();
        
        return $this->successResponse("$deleted notifications expirées supprimées");
    }

    /**
     * Tester l'envoi d'une notification
     */
    public function test(Request $request): JsonResponse
    {
        $request->validate([
            'channel' => 'required|in:email,sms,push,database'
        ]);

        try {
            $user = auth()->user();
            
            $notification = $this->notificationService->sendToUser($user, [
                'type' => 'system_alert',
                'title' => 'Test de notification',
                'message' => 'Ceci est un test du système de notifications CRFM.',
                'channels' => [$request->channel],
                'priority' => 'normal'
            ]);

            return $this->successResponse('Notification de test envoyée', [
                'notification_id' => $notification->id,
                'channel' => $request->channel
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors du test : ' . $e->getMessage());
        }
    }
}
