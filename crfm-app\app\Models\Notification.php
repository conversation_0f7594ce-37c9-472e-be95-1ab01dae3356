<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Carbon\Carbon;

class Notification extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'notifiable_type',
        'notifiable_id',
        'type',
        'title',
        'message',
        'data',
        'channels',
        'status',
        'sent_at',
        'read_at',
        'priority',
        'scheduled_at',
        'expires_at'
    ];

    protected $casts = [
        'data' => 'array',
        'channels' => 'array',
        'sent_at' => 'datetime',
        'read_at' => 'datetime',
        'scheduled_at' => 'datetime',
        'expires_at' => 'datetime'
    ];

    // Constantes pour les types de notifications
    const TYPE_COTISATION_RAPPEL = 'cotisation_rappel';
    const TYPE_COTISATION_RETARD = 'cotisation_retard';
    const TYPE_DOCUMENT_MANQUANT = 'document_manquant';
    const TYPE_RDV_CONVOCATION = 'rdv_convocation';
    const TYPE_PENSION_ETAPE = 'pension_etape';
    const TYPE_PENSION_LIQUIDEE = 'pension_liquidee';
    const TYPE_INFO_GENERALE = 'info_generale';
    const TYPE_SYSTEM_ALERT = 'system_alert';

    // Constantes pour les canaux
    const CHANNEL_EMAIL = 'email';
    const CHANNEL_SMS = 'sms';
    const CHANNEL_PUSH = 'push';
    const CHANNEL_DATABASE = 'database';

    // Constantes pour les statuts
    const STATUS_PENDING = 'pending';
    const STATUS_SENT = 'sent';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';

    // Constantes pour les priorités
    const PRIORITY_LOW = 'low';
    const PRIORITY_NORMAL = 'normal';
    const PRIORITY_HIGH = 'high';
    const PRIORITY_URGENT = 'urgent';

    /**
     * Relation avec l'utilisateur destinataire
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Relation polymorphe avec l'objet notifiable
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope pour les notifications non lues
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope pour les notifications lues
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope pour les notifications par type
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope pour les notifications par priorité
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope pour les notifications en attente
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * Scope pour les notifications envoyées
     */
    public function scopeSent($query)
    {
        return $query->where('status', self::STATUS_SENT);
    }

    /**
     * Marquer comme lue
     */
    public function markAsRead(): void
    {
        $this->update(['read_at' => now()]);
    }

    /**
     * Marquer comme non lue
     */
    public function markAsUnread(): void
    {
        $this->update(['read_at' => null]);
    }

    /**
     * Vérifier si la notification est lue
     */
    public function isRead(): bool
    {
        return !is_null($this->read_at);
    }

    /**
     * Vérifier si la notification est expirée
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Obtenir la couleur selon la priorité
     */
    public function getPriorityColorAttribute(): string
    {
        return match($this->priority) {
            self::PRIORITY_LOW => 'secondary',
            self::PRIORITY_NORMAL => 'primary',
            self::PRIORITY_HIGH => 'warning',
            self::PRIORITY_URGENT => 'danger',
            default => 'primary'
        };
    }

    /**
     * Obtenir l'icône selon le type
     */
    public function getTypeIconAttribute(): string
    {
        return match($this->type) {
            self::TYPE_COTISATION_RAPPEL => 'feather icon-clock',
            self::TYPE_COTISATION_RETARD => 'feather icon-alert-triangle',
            self::TYPE_DOCUMENT_MANQUANT => 'feather icon-file-minus',
            self::TYPE_RDV_CONVOCATION => 'feather icon-calendar',
            self::TYPE_PENSION_ETAPE => 'feather icon-trending-up',
            self::TYPE_PENSION_LIQUIDEE => 'feather icon-check-circle',
            self::TYPE_INFO_GENERALE => 'feather icon-info',
            self::TYPE_SYSTEM_ALERT => 'feather icon-alert-circle',
            default => 'feather icon-bell'
        };
    }

    /**
     * Obtenir le temps écoulé depuis la création
     */
    public function getTimeAgoAttribute(): string
    {
        return $this->created_at->diffForHumans();
    }
}
