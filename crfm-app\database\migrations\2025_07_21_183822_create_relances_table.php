<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('relances', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');
            $table->foreignId('cotisation_id')->constrained()->onDelete('cascade');

            $table->enum('type_relance', ['premiere', 'deuxieme', 'derniere', 'mise_en_demeure']);
            $table->date('date_relance');
            $table->timestamp('date_envoi')->nullable();
            $table->enum('canal_envoi', ['email', 'sms', 'courrier', 'telephone']);
            $table->text('message');
            $table->enum('statut', ['programmee', 'envoyee', 'recue', 'sans_reponse'])->default('programmee');
            $table->timestamp('date_reponse')->nullable();
            $table->text('observations')->nullable();
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');

            $table->timestamps();
            $table->softDeletes();

            $table->index(['adherent_id', 'type_relance']);
            $table->index(['cotisation_id']);
            $table->index(['date_relance']);
            $table->index(['statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('relances');
    }
};
