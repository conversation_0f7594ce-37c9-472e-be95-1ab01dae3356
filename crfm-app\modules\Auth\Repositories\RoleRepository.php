<?php

namespace Modules\Auth\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use Modules\Auth\Models\Role;
use Illuminate\Database\Eloquent\Collection;

class RoleRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(Role $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active roles
     */
    public function getActiveRoles(): Collection
    {
        return $this->model->active()->orderBy('level', 'desc')->get();
    }

    /**
     * Get roles with permissions
     */
    public function getRolesWithPermissions(): Collection
    {
        return $this->model->with('permissions')->get();
    }

    /**
     * Find role by slug
     */
    public function findBySlug(string $slug): ?Role
    {
        return $this->model->where('slug', $slug)->first();
    }

    /**
     * Get roles by level
     */
    public function getRolesByLevel(int $level): Collection
    {
        return $this->model->where('level', $level)->get();
    }

    /**
     * Get roles with user count
     */
    public function getRolesWithUserCount(): Collection
    {
        return $this->model->withCount('users')->get();
    }
}
