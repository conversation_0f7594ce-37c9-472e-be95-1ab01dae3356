@extends('layouts.app')

@section('title', 'Test Affichage Dashboard')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Test de l'Affichage des Statistiques Dashboard</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active">Test Affichage</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Alerte d'information -->
    <div class="row">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="feather icon-info"></i> Test des Statistiques</h5>
                <p>Cette page teste l'affichage des statistiques du dashboard avec les mêmes styles CSS.</p>
                <p><strong>Statistiques recherchées :</strong></p>
                <ul>
                    <li>✅ Adhérents suspendus : {{ $stats['adherents_suspendus'] ?? 'Non défini' }}</li>
                    <li>✅ Total cotisations payées : {{ isset($stats['montant_total_cotisations']) ? number_format($stats['montant_total_cotisations']) . ' €' : 'Non défini' }}</li>
                    <li>✅ Cotisations en retard : {{ $stats['cotisations_en_retard'] ?? 'Non défini' }}</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Reproduction exacte des cartes du dashboard -->
    <div class="row quick-stats">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-blue text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['adherents_actifs']) ? number_format($stats['adherents_actifs']) : '0' }}</h4>
                            <h6 class="text-white m-b-0">Adhérents actifs</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-users f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-green text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">3,452</h4>
                            <h6 class="text-white m-b-0">Retraités</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-award f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-yellow text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['cotisations_mois']) ? number_format($stats['cotisations_mois']) . ' €' : '0 €' }}</h4>
                            <h6 class="text-white m-b-0">Cotisations ce mois</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-bar-chart f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-red text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['cotisations_en_retard']) ? number_format($stats['cotisations_en_retard']) : '0' }}</h4>
                            <h6 class="text-white m-b-0">Cotisations en retard</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-alert-triangle f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistiques supplémentaires -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-green text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['total_adherents']) ? number_format($stats['total_adherents']) : '0' }}</h4>
                            <h6 class="text-white m-b-0">Total adhérents</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-users f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-orange text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['adherents_suspendus']) ? number_format($stats['adherents_suspendus']) : '0' }}</h4>
                            <h6 class="text-white m-b-0">Adhérents suspendus</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-user-x f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-purple text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['montant_total_cotisations']) ? number_format($stats['montant_total_cotisations']) . ' €' : '0 €' }}</h4>
                            <h6 class="text-white m-b-0">Total cotisations payées</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-dollar-sign f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-c-pink text-white">
                <div class="card-block">
                    <div class="row align-items-center">
                        <div class="col-8">
                            <h4 class="text-white f-w-600">{{ isset($stats['pensions_liquidees']) ? number_format($stats['pensions_liquidees']) : '0' }}</h4>
                            <h6 class="text-white m-b-0">Pensions liquidées</h6>
                        </div>
                        <div class="col-4 text-end">
                            <i class="feather icon-check-circle f-28 text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Test de contraste -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Test de Contraste des Couleurs</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-blue text-white text-center p-3">
                                <h6 class="text-white">Bleu</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-green text-white text-center p-3">
                                <h6 class="text-white">Vert</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-yellow text-white text-center p-3">
                                <h6 class="text-white">Jaune</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-red text-white text-center p-3">
                                <h6 class="text-white">Rouge</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-orange text-white text-center p-3">
                                <h6 class="text-white">Orange</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                        <div class="col-md-2 mb-3">
                            <div class="card bg-c-purple text-white text-center p-3">
                                <h6 class="text-white">Violet</h6>
                                <small class="text-white">Lisible ?</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Données brutes pour debug -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5>Données Brutes (Debug)</h5>
                </div>
                <div class="card-body">
                    <pre>{{ print_r($stats, true) }}</pre>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Styles pour les cartes colorées du dashboard */
.bg-c-blue {
    background: linear-gradient(45deg, #4099ff, #73b4ff) !important;
    color: white !important;
}
.bg-c-green {
    background: linear-gradient(45deg, #2ed8b6, #59e0c5) !important;
    color: white !important;
}
.bg-c-yellow {
    background: linear-gradient(45deg, #FFB64D, #ffcb80) !important;
    color: white !important;
}
.bg-c-red {
    background: linear-gradient(45deg, #FF5370, #ff869a) !important;
    color: white !important;
}
.bg-c-orange {
    background: linear-gradient(45deg, #FF8A65, #ffab91) !important;
    color: white !important;
}
.bg-c-purple {
    background: linear-gradient(45deg, #9C27B0, #ba68c8) !important;
    color: white !important;
}
.bg-c-pink {
    background: linear-gradient(45deg, #E91E63, #f06292) !important;
    color: white !important;
}

/* Forcer le texte blanc sur les cartes colorées */
.bg-c-blue *, .bg-c-green *, .bg-c-yellow *, .bg-c-red *,
.bg-c-orange *, .bg-c-purple *, .bg-c-pink * {
    color: white !important;
}

/* Styles pour les icônes */
.f-28 {
    font-size: 28px !important;
}
.f-w-600 {
    font-weight: 600 !important;
}
.m-b-0 {
    margin-bottom: 0 !important;
}

/* Assurer la visibilité du texte */
.text-white {
    color: white !important;
}

/* Style pour les cartes avec bordure arrondie */
.card {
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.card-block {
    padding: 20px;
}
</style>
@endsection
