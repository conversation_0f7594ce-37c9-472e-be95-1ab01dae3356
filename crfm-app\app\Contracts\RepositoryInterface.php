<?php

namespace App\Contracts;

use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;

interface RepositoryInterface
{
    /**
     * Get all records
     */
    public function all(): Collection;

    /**
     * Find record by ID
     */
    public function find(int $id): ?Model;

    /**
     * Find record by UUID
     */
    public function findByUuid(string $uuid): ?Model;

    /**
     * Find record or fail
     */
    public function findOrFail(int $id): Model;

    /**
     * Create new record
     */
    public function create(array $data): Model;

    /**
     * Update record
     */
    public function update(Model $model, array $data): bool;

    /**
     * Delete record
     */
    public function delete(Model $model): bool;

    /**
     * Get paginated records
     */
    public function paginate(int $perPage = 15): LengthAwarePaginator;

    /**
     * Search records
     */
    public function search(string $term, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get records with conditions
     */
    public function where(array $conditions): Collection;

    /**
     * Count records
     */
    public function count(): int;
}
