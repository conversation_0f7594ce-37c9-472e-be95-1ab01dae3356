<?php

namespace Modules\Auth\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Auth\Models\Role;
use Modules\Auth\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class RoleController extends BaseController
{
    /**
     * Display a listing of roles
     */
    public function index(): View
    {
        $roles = Role::with(['permissions'])->paginate(15);
        return view('auth::admin.roles.index', compact('roles'));
    }

    /**
     * Show the form for creating a new role
     */
    public function create(): View
    {
        $permissions = Permission::all()->groupBy('module');
        return view('auth::admin.roles.create', compact('permissions'));
    }

    /**
     * Store a newly created role
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles',
            'description' => 'nullable|string|max:500',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role = Role::create([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        $role->permissions()->sync($request->permissions);

        return $this->successRedirect('auth.admin.roles.index', 'Rôle créé avec succès !');
    }

    /**
     * Display the specified role
     */
    public function show(Role $role): View
    {
        $role->load(['permissions', 'users']);
        return view('auth::admin.roles.show', compact('role'));
    }

    /**
     * Show the form for editing the specified role
     */
    public function edit(Role $role): View
    {
        $permissions = Permission::all()->groupBy('module');
        $role->load('permissions');
        return view('auth::admin.roles.edit', compact('role', 'permissions'));
    }

    /**
     * Update the specified role
     */
    public function update(Request $request, Role $role): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255|unique:roles,name,' . $role->id,
            'description' => 'nullable|string|max:500',
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,id',
        ]);

        $role->update([
            'name' => $request->name,
            'description' => $request->description,
        ]);

        $role->permissions()->sync($request->permissions);

        return $this->successRedirect('auth.admin.roles.index', 'Rôle mis à jour avec succès !');
    }

    /**
     * Remove the specified role
     */
    public function destroy(Role $role): RedirectResponse
    {
        // Prevent deletion of admin role
        if ($role->name === 'admin') {
            return $this->backWithError('Le rôle administrateur ne peut pas être supprimé !');
        }

        // Check if role has users
        if ($role->users()->count() > 0) {
            return $this->backWithError('Ce rôle ne peut pas être supprimé car il est assigné à des utilisateurs !');
        }

        $role->delete();

        return $this->successRedirect('auth.admin.roles.index', 'Rôle supprimé avec succès !');
    }
}
