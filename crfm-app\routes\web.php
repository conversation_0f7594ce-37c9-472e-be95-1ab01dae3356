<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('/', function () {
    if (auth()->check()) {
        return redirect()->route('dashboard.index');
    }
    return redirect()->route('auth.login');
});

// Routes pour les pages principales (temporaires - seront remplacées par les modules)
Route::middleware(['web', 'auth'])->group(function () {
    // Routes temporaires pour les liens du menu
    Route::get('/affiliation', function () {
        return redirect()->route('adherents.index');
    })->name('affiliation.index');

    Route::get('/declaration', function () {
        return view('pages.declaration');
    })->name('declaration.index');

    Route::get('/pre-liquidation', function () {
        return view('pages.pre-liquidation');
    })->name('pre-liquidation.index');

    Route::get('/statistiques', function () {
        return view('pages.statistiques');
    })->name('statistiques.index');

    Route::get('/parametres', function () {
        return redirect()->route('users.index');
    })->name('parametres.index');

    // Routes pour la gestion des utilisateurs
    Route::resource('users', App\Http\Controllers\UserController::class);
    Route::post('/users/{user}/toggle-status', [App\Http\Controllers\UserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('/users/{user}/reset-password', [App\Http\Controllers\UserController::class, 'resetPassword'])->name('users.reset-password');
    Route::post('/users/{id}/restore', [App\Http\Controllers\UserController::class, 'restore'])->name('users.restore');

    Route::get('/outils', function () {
        return view('pages.outils');
    })->name('outils.index');

    // API Routes
    Route::get('/api/dashboard/stats', function () {
        return response()->json([
            'success' => true,
            'data' => [
                'total_adherents' => rand(8700, 8800),
                'cotisations_mois' => rand(15000000, 16000000),
                'pensions_en_cours' => rand(40, 50),
                'utilisateurs_actifs' => rand(5, 10)
            ]
        ]);
    })->name('api.dashboard.stats');

    // Routes pour les notifications
    Route::prefix('notifications')->name('notifications.')->group(function () {
        Route::get('/', [App\Http\Controllers\NotificationController::class, 'index'])->name('index');
    });

    // Routes pour les workflows
    Route::prefix('admin/workflows')->name('workflows.')->middleware(['auth', 'role:admin'])->group(function () {
        Route::get('/', [App\Http\Controllers\WorkflowController::class, 'index'])->name('index');
    });

    // Route de test pour les fonctions JavaScript
    Route::get('/test/javascript', function () {
        return view('test.javascript');
    })->name('test.javascript')->middleware('auth');

    // Route de test pour les statistiques du dashboard
    Route::get('/test/dashboard-stats', function () {
        $service = app(\Modules\Dashboard\Services\DashboardService::class);
        $stats = $service->getQuickStats();

        return response()->json([
            'success' => true,
            'data' => $stats,
            'formatted' => [
                'total_adherents' => number_format($stats['total_adherents']),
                'adherents_actifs' => number_format($stats['adherents_actifs']),
                'adherents_suspendus' => number_format($stats['adherents_suspendus']),
                'cotisations_mois' => number_format($stats['cotisations_mois']) . ' €',
                'cotisations_payees' => number_format($stats['cotisations_payees']),
                'cotisations_en_retard' => number_format($stats['cotisations_en_retard']),
                'montant_total_cotisations' => number_format($stats['montant_total_cotisations']) . ' €',
                'pensions_en_cours' => number_format($stats['pensions_en_cours']),
                'pensions_liquidees' => number_format($stats['pensions_liquidees']),
                'utilisateurs_actifs' => number_format($stats['utilisateurs_actifs'])
            ]
        ]);
    })->name('test.dashboard-stats');

    // Route de test pour vérifier l'affichage du dashboard
    Route::get('/test/dashboard-display', function () {
        $service = app(\Modules\Dashboard\Services\DashboardService::class);
        $stats = $service->getQuickStats();

        return view('test.dashboard-display', compact('stats'));
    })->name('test.dashboard-display')->middleware('auth');

    // API Routes pour les notifications
    Route::prefix('api/notifications')->name('api.notifications.')->group(function () {
        Route::get('/unread', [App\Http\Controllers\NotificationController::class, 'getUnread'])->name('unread');
        Route::post('/{notification}/read', [App\Http\Controllers\NotificationController::class, 'markAsRead'])->name('mark-read');
        Route::post('/mark-all-read', [App\Http\Controllers\NotificationController::class, 'markAllAsRead'])->name('mark-all-read');
        Route::delete('/{notification}', [App\Http\Controllers\NotificationController::class, 'destroy'])->name('delete');
        Route::post('/send', [App\Http\Controllers\NotificationController::class, 'send'])->name('send');
        Route::post('/send-to-adherent', [App\Http\Controllers\NotificationController::class, 'sendToAdherent'])->name('send-to-adherent');
        Route::get('/statistics', [App\Http\Controllers\NotificationController::class, 'getStatistics'])->name('statistics');
        Route::post('/test', [App\Http\Controllers\NotificationController::class, 'test'])->name('test');
        Route::delete('/cleanup', [App\Http\Controllers\NotificationController::class, 'cleanup'])->name('cleanup');
    });

    // API Routes pour les workflows
    Route::prefix('api/workflows')->name('api.workflows.')->middleware(['auth', 'role:admin'])->group(function () {
        Route::post('/daily', [App\Http\Controllers\WorkflowController::class, 'runDailyWorkflows'])->name('daily');
        Route::post('/reminders', [App\Http\Controllers\WorkflowController::class, 'runReminders'])->name('reminders');
        Route::get('/report', [App\Http\Controllers\WorkflowController::class, 'getReport'])->name('report');
        Route::post('/command', [App\Http\Controllers\WorkflowController::class, 'runCommand'])->name('command');
        Route::post('/schedule', [App\Http\Controllers\WorkflowController::class, 'scheduleWorkflows'])->name('schedule');
        Route::get('/stats', [App\Http\Controllers\WorkflowController::class, 'getLiveStats'])->name('stats');
        Route::post('/test-notifications', [App\Http\Controllers\WorkflowController::class, 'testNotifications'])->name('test-notifications');
        Route::post('/cleanup', [App\Http\Controllers\WorkflowController::class, 'cleanup'])->name('cleanup');
    });

    // API pour l'historique des cotisations d'un adhérent
    Route::get('/api/adherents/{adherent}/cotisations-history', function ($adherentId) {
        // Simulation de données - à remplacer par la vraie logique
        return response()->json([
            [
                'periode' => 'Jan 2024',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Déc 2023',
                'montant' => '45,000',
                'status' => 'Payée',
                'status_color' => 'success'
            ],
            [
                'periode' => 'Nov 2023',
                'montant' => '42,000',
                'status' => 'En retard',
                'status_color' => 'danger'
            ]
        ]);
    })->name('api.adherents.cotisations-history');

    // API pour les informations de pension d'un adhérent
    Route::get('/api/adherents/{adherent}/pension-info', function ($adherentId) {
        // Simulation de données - à remplacer par la vraie logique
        return response()->json([
            'success' => true,
            'salaire_base' => 250000,
            'annees_service' => 25,
            'age' => 58,
            'eligible_retraite' => true
        ]);
    })->name('api.adherents.pension-info');
});
