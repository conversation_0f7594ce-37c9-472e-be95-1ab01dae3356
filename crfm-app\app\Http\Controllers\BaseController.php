<?php

namespace App\Http\Controllers;

use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Routing\Controller;

abstract class BaseController extends Controller
{
    use AuthorizesRequests, ValidatesRequests;

    /**
     * Success response
     */
    protected function successResponse(string $message = 'Opération réussie', array $data = []): JsonResponse
    {
        return response()->json([
            'success' => true,
            'message' => $message,
            'data' => $data
        ]);
    }

    /**
     * Error response
     */
    protected function errorResponse(string $message = 'Une erreur est survenue', array $errors = [], int $code = 400): JsonResponse
    {
        return response()->json([
            'success' => false,
            'message' => $message,
            'errors' => $errors
        ], $code);
    }

    /**
     * Success redirect with message
     */
    protected function successRedirect(string $route, string $message = 'Opération réussie'): RedirectResponse
    {
        return redirect()->route($route)->with('success', $message);
    }

    /**
     * Error redirect with message
     */
    protected function errorRedirect(string $route, string $message = 'Une erreur est survenue'): RedirectResponse
    {
        return redirect()->route($route)->with('error', $message);
    }

    /**
     * Back redirect with message
     */
    protected function backWithSuccess(string $message = 'Opération réussie'): RedirectResponse
    {
        return back()->with('success', $message);
    }

    /**
     * Back redirect with error
     */
    protected function backWithError(string $message = 'Une erreur est survenue'): RedirectResponse
    {
        return back()->with('error', $message);
    }
}
