<?php

namespace Modules\Reports\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Reports\Services\ReportService;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\Response;

class ReportController extends BaseController
{
    protected ReportService $reportService;

    public function __construct(ReportService $reportService)
    {
        $this->reportService = $reportService;
    }

    /**
     * Display reports dashboard
     */
    public function index(): View
    {
        $summary = $this->reportService->getDashboardSummary();
        
        return view('reports::index', compact('summary'));
    }

    /**
     * Show adherents report form
     */
    public function adherentsForm(): View
    {
        return view('reports::adherents.form');
    }

    /**
     * Generate adherents report
     */
    public function adherentsReport(Request $request): View
    {
        $filters = $request->only(['statut', 'employeur', 'date_from', 'date_to', 'format']);
        $report = $this->reportService->generateAdherentsReport($filters);

        if ($request->format === 'pdf') {
            return $this->generatePDF('reports::adherents.pdf', $report, 'rapport_adherents.pdf');
        }

        if ($request->format === 'excel') {
            return $this->generateExcel($report['adherents'], 'rapport_adherents.xlsx');
        }

        return view('reports::adherents.report', compact('report'));
    }

    /**
     * Show cotisations report form
     */
    public function cotisationsForm(): View
    {
        return view('reports::cotisations.form');
    }

    /**
     * Generate cotisations report
     */
    public function cotisationsReport(Request $request): View
    {
        $filters = $request->only(['annee', 'mois', 'statut', 'employeur', 'format']);
        $report = $this->reportService->generateCotisationsReport($filters);

        if ($request->format === 'pdf') {
            return $this->generatePDF('reports::cotisations.pdf', $report, 'rapport_cotisations.pdf');
        }

        if ($request->format === 'excel') {
            return $this->generateExcel($report['cotisations'], 'rapport_cotisations.xlsx');
        }

        return view('reports::cotisations.report', compact('report'));
    }

    /**
     * Show pensions report form
     */
    public function pensionsForm(): View
    {
        return view('reports::pensions.form');
    }

    /**
     * Generate pensions report
     */
    public function pensionsReport(Request $request): View
    {
        $filters = $request->only(['annee', 'statut', 'type_pension', 'format']);
        $report = $this->reportService->generatePensionsReport($filters);

        if ($request->format === 'pdf') {
            return $this->generatePDF('reports::pensions.pdf', $report, 'rapport_pensions.pdf');
        }

        if ($request->format === 'excel') {
            return $this->generateExcel($report['dossiers'], 'rapport_pensions.xlsx');
        }

        return view('reports::pensions.report', compact('report'));
    }

    /**
     * Show financial summary form
     */
    public function financialForm(): View
    {
        return view('reports::financial.form');
    }

    /**
     * Generate financial summary report
     */
    public function financialReport(Request $request): View
    {
        $filters = $request->only(['annee', 'format']);
        $report = $this->reportService->generateFinancialSummary($filters);

        if ($request->format === 'pdf') {
            return $this->generatePDF('reports::financial.pdf', $report, 'rapport_financier.pdf');
        }

        return view('reports::financial.report', compact('report'));
    }

    /**
     * Generate custom report
     */
    public function customReport(Request $request): View
    {
        $request->validate([
            'report_type' => 'required|in:adherents,cotisations,pensions,financial',
            'filters' => 'array',
        ]);

        $filters = $request->input('filters', []);
        
        $report = match($request->report_type) {
            'adherents' => $this->reportService->generateAdherentsReport($filters),
            'cotisations' => $this->reportService->generateCotisationsReport($filters),
            'pensions' => $this->reportService->generatePensionsReport($filters),
            'financial' => $this->reportService->generateFinancialSummary($filters),
        };

        return view('reports::custom', compact('report', 'request'));
    }

    /**
     * Export report data as JSON for API
     */
    public function exportData(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:adherents,cotisations,pensions,financial',
            'filters' => 'array',
        ]);

        $filters = $request->input('filters', []);
        
        $report = match($request->report_type) {
            'adherents' => $this->reportService->generateAdherentsReport($filters),
            'cotisations' => $this->reportService->generateCotisationsReport($filters),
            'pensions' => $this->reportService->generatePensionsReport($filters),
            'financial' => $this->reportService->generateFinancialSummary($filters),
        };

        return $this->successResponse('Rapport généré avec succès', $report);
    }

    /**
     * Get dashboard statistics for AJAX
     */
    public function getDashboardStats()
    {
        $summary = $this->reportService->getDashboardSummary();
        
        return $this->successResponse('Statistiques récupérées', $summary);
    }

    /**
     * Generate PDF report
     */
    private function generatePDF(string $view, array $data, string $filename): Response
    {
        // This would require a PDF library like DomPDF or wkhtmltopdf
        // For now, we'll return a simple response
        
        $html = view($view, $data)->render();
        
        return response($html)
            ->header('Content-Type', 'text/html')
            ->header('Content-Disposition', "attachment; filename=\"{$filename}\"");
    }

    /**
     * Generate Excel report
     */
    private function generateExcel($data, string $filename): Response
    {
        // This would require a library like PhpSpreadsheet
        // For now, we'll generate CSV
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            if ($data->isNotEmpty()) {
                // Add headers based on first item
                $firstItem = $data->first();
                if (is_object($firstItem)) {
                    $headers = array_keys($firstItem->toArray());
                } else {
                    $headers = array_keys($firstItem);
                }
                fputcsv($file, $headers, ';');
                
                // Add data
                foreach ($data as $item) {
                    if (is_object($item)) {
                        fputcsv($file, $item->toArray(), ';');
                    } else {
                        fputcsv($file, $item, ';');
                    }
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show report builder interface
     */
    public function builder(): View
    {
        return view('reports::builder');
    }

    /**
     * Preview report before generation
     */
    public function preview(Request $request)
    {
        $request->validate([
            'report_type' => 'required|in:adherents,cotisations,pensions,financial',
            'filters' => 'array',
        ]);

        $filters = $request->input('filters', []);
        
        // Generate a limited preview (first 10 records)
        $report = match($request->report_type) {
            'adherents' => $this->reportService->generateAdherentsReport($filters),
            'cotisations' => $this->reportService->generateCotisationsReport($filters),
            'pensions' => $this->reportService->generatePensionsReport($filters),
            'financial' => $this->reportService->generateFinancialSummary($filters),
        };

        // Limit data for preview
        if (isset($report['adherents'])) {
            $report['adherents'] = $report['adherents']->take(10);
        }
        if (isset($report['cotisations'])) {
            $report['cotisations'] = $report['cotisations']->take(10);
        }
        if (isset($report['dossiers'])) {
            $report['dossiers'] = $report['dossiers']->take(10);
        }

        return $this->successResponse('Aperçu généré', $report);
    }
}
