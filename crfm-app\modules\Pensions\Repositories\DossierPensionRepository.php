<?php

namespace Modules\Pensions\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use Modules\Pensions\Models\DossierPension;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class DossierPensionRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(DossierPension $model)
    {
        parent::__construct($model);
    }

    /**
     * Get paginated dossiers with filters
     */
    public function getPaginatedDossiers(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->with(['adherent', 'creator', 'validator']);

        // Apply filters
        if (!empty($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('numero_dossier', 'LIKE', "%{$filters['search']}%")
                  ->orWhereHas('adherent', function ($subQ) use ($filters) {
                      $subQ->where('nom', 'LIKE', "%{$filters['search']}%")
                           ->orWhere('prenoms', 'LIKE', "%{$filters['search']}%")
                           ->orWhere('numero_adherent', 'LIKE', "%{$filters['search']}%");
                  });
            });
        }

        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['type_pension'])) {
            $query->byType($filters['type_pension']);
        }

        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        if (!empty($filters['date_from'])) {
            $query->where('date_demande', '>=', $filters['date_from']);
        }

        if (!empty($filters['date_to'])) {
            $query->where('date_demande', '<=', $filters['date_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Get dossiers statistics
     */
    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'en_cours' => $this->model->pending()->count(),
            'valides' => $this->model->validated()->count(),
            'liquides' => $this->model->liquidated()->count(),
            'vieillesse' => $this->model->byType(DossierPension::TYPE_VIEILLESSE)->count(),
            'invalidite' => $this->model->byType(DossierPension::TYPE_INVALIDITE)->count(),
            'survivant' => $this->model->byType(DossierPension::TYPE_SURVIVANT)->count(),
            'anticipee' => $this->model->byType(DossierPension::TYPE_ANTICIPEE)->count(),
        ];
    }

    /**
     * Get dossiers by status
     */
    public function getDossiersByStatus(string $status): Collection
    {
        return $this->model->where('statut', $status)
                          ->with(['adherent'])
                          ->orderBy('date_demande', 'desc')
                          ->get();
    }

    /**
     * Get dossiers ready for liquidation
     */
    public function getDossiersReadyForLiquidation(): Collection
    {
        return $this->model->validated()
                          ->whereHas('preLiquidation', function ($query) {
                              $query->where('statut', 'valide');
                          })
                          ->with(['adherent', 'preLiquidation'])
                          ->get();
    }

    /**
     * Get recent dossiers
     */
    public function getRecentDossiers(int $days = 30): Collection
    {
        return $this->model->where('created_at', '>=', now()->subDays($days))
                          ->with(['adherent'])
                          ->orderBy('created_at', 'desc')
                          ->get();
    }

    /**
     * Generate next dossier number
     */
    public function generateNextNumero(): string
    {
        return DossierPension::generateNextNumero();
    }

    /**
     * Get dossiers with missing documents
     */
    public function getDossiersWithMissingDocuments(): Collection
    {
        return $this->model->whereHas('documents', function ($query) {
                              $query->where('is_required', true)
                                    ->where('is_verified', false);
                          })
                          ->with(['adherent', 'documents'])
                          ->get();
    }

    /**
     * Get monthly statistics for chart
     */
    public function getMonthlyStatistics(int $year): array
    {
        $statistics = [];
        
        for ($month = 1; $month <= 12; $month++) {
            $dossiers = $this->model->whereYear('date_demande', $year)
                                   ->whereMonth('date_demande', $month);
            
            $statistics[] = [
                'month' => $month,
                'total_dossiers' => $dossiers->count(),
                'dossiers_liquides' => $dossiers->liquidated()->count(),
                'montant_total_liquide' => $dossiers->liquidated()->sum('montant_pension_liquide'),
            ];
        }

        return $statistics;
    }

    /**
     * Get dossiers by pension type
     */
    public function getDossiersByType(string $type): Collection
    {
        return $this->model->byType($type)
                          ->with(['adherent'])
                          ->orderBy('date_demande', 'desc')
                          ->get();
    }

    /**
     * Get average processing time
     */
    public function getAverageProcessingTime(): array
    {
        $liquidatedDossiers = $this->model->liquidated()
                                         ->whereNotNull('liquidated_at')
                                         ->get();

        $totalDays = 0;
        $count = 0;

        foreach ($liquidatedDossiers as $dossier) {
            $processingDays = $dossier->date_demande->diffInDays($dossier->liquidated_at);
            $totalDays += $processingDays;
            $count++;
        }

        return [
            'average_days' => $count > 0 ? round($totalDays / $count, 1) : 0,
            'total_processed' => $count,
        ];
    }

    /**
     * Get dossiers for export
     */
    public function getDossiersForExport(array $filters = []): Collection
    {
        $query = $this->model->with(['adherent', 'preLiquidation']);

        // Apply same filters as pagination
        if (!empty($filters['statut'])) {
            $query->where('statut', $filters['statut']);
        }

        if (!empty($filters['type_pension'])) {
            $query->byType($filters['type_pension']);
        }

        if (!empty($filters['annee'])) {
            $query->byYear($filters['annee']);
        }

        return $query->orderBy('date_demande', 'desc')->get();
    }

    /**
     * Get workflow progress for dossier
     */
    public function getWorkflowProgress(DossierPension $dossier): array
    {
        $steps = $dossier->workflowSteps()->orderBy('step_order')->get();
        
        $totalSteps = $steps->count();
        $completedSteps = $steps->where('statut', 'termine')->count();
        $currentStep = $steps->where('statut', 'en_cours')->first();
        
        return [
            'total_steps' => $totalSteps,
            'completed_steps' => $completedSteps,
            'progress_percentage' => $totalSteps > 0 ? round(($completedSteps / $totalSteps) * 100, 1) : 0,
            'current_step' => $currentStep,
            'next_step' => $currentStep ? $steps->where('step_order', '>', $currentStep->step_order)->first() : null,
        ];
    }
}
