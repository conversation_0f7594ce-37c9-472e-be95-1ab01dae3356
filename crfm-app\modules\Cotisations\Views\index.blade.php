@extends('layouts.template')

@section('title', 'Gestion des Cotisations - CRFM')

@section('page-header')
@section('page-title', 'Gestion des Cotisations')
@section('page-description', 'Suivi et gestion des cotisations des adhérents')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Cotisations</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Statistiques rapides -->
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-green text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['total_payees'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">Cotisations payées</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-check-circle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-yellow text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['total_en_attente'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">En attente</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-clock f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-red text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['total_en_retard'] ?? 0) }}</h4>
                        <h6 class="text-white m-b-0">En retard</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-alert-triangle f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-3 col-md-6">
        <div class="card bg-c-blue text-white">
            <div class="card-block">
                <div class="row align-items-center">
                    <div class="col-8">
                        <h4 class="text-white f-w-600">{{ number_format($statistics['montant_total'] ?? 0) }} €</h4>
                        <h6 class="text-white m-b-0">Montant total</h6>
                    </div>
                    <div class="col-4 text-end">
                        <i class="feather icon-dollar-sign f-28"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Filtres et recherche -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-filter text-c-blue me-2"></i>Filtres et recherche</h5>
                <div class="card-header-right">
                    <a href="{{ route('cotisations.create') }}" class="btn btn-primary btn-sm">
                        <i class="feather icon-plus me-1"></i>Nouvelle cotisation
                    </a>
                </div>
            </div>
            <div class="card-block">
                <form method="GET" action="{{ route('cotisations.index') }}">
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Recherche</label>
                                <input type="text" name="search" class="form-control" 
                                       placeholder="Numéro, référence..." 
                                       value="{{ $filters['search'] ?? '' }}">
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Statut</label>
                                <select name="statut" class="form-control">
                                    <option value="">Tous</option>
                                    <option value="en_attente" {{ ($filters['statut'] ?? '') === 'en_attente' ? 'selected' : '' }}>En attente</option>
                                    <option value="payee" {{ ($filters['statut'] ?? '') === 'payee' ? 'selected' : '' }}>Payée</option>
                                    <option value="en_retard" {{ ($filters['statut'] ?? '') === 'en_retard' ? 'selected' : '' }}>En retard</option>
                                    <option value="annulee" {{ ($filters['statut'] ?? '') === 'annulee' ? 'selected' : '' }}>Annulée</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Année</label>
                                <select name="annee" class="form-control">
                                    <option value="">Toutes</option>
                                    @for($year = date('Y'); $year >= date('Y') - 5; $year--)
                                        <option value="{{ $year }}" {{ ($filters['annee'] ?? '') == $year ? 'selected' : '' }}>{{ $year }}</option>
                                    @endfor
                                </select>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="form-group">
                                <label>Mois</label>
                                <select name="mois" class="form-control">
                                    <option value="">Tous</option>
                                    @php
                                        $months = [
                                            1 => 'Janvier', 2 => 'Février', 3 => 'Mars', 4 => 'Avril',
                                            5 => 'Mai', 6 => 'Juin', 7 => 'Juillet', 8 => 'Août',
                                            9 => 'Septembre', 10 => 'Octobre', 11 => 'Novembre', 12 => 'Décembre'
                                        ];
                                    @endphp
                                    @foreach($months as $num => $name)
                                        <option value="{{ $num }}" {{ ($filters['mois'] ?? '') == $num ? 'selected' : '' }}>
                                            {{ $name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>&nbsp;</label>
                                <div class="btn-group btn-block">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="feather icon-search"></i>
                                    </button>
                                    <a href="{{ route('cotisations.index') }}" class="btn btn-secondary">
                                        <i class="feather icon-refresh-cw"></i>
                                    </a>
                                    <a href="{{ route('cotisations.export', $filters) }}" class="btn btn-success">
                                        <i class="feather icon-download"></i>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Liste des cotisations -->
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-blue me-2"></i>Liste des cotisations</h5>
                <div class="card-header-right">
                    <span class="badge bg-info">{{ $cotisations->total() }} résultat(s)</span>
                </div>
            </div>
            <div class="card-block">
                @if($cotisations->count() > 0)
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>N° Cotisation</th>
                                <th>Adhérent</th>
                                <th>Période</th>
                                <th>Montant</th>
                                <th>Échéance</th>
                                <th>Statut</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cotisations as $cotisation)
                            <tr class="{{ $cotisation->isOverdue() ? 'table-danger' : '' }}">
                                <td>
                                    <strong>{{ $cotisation->numero_cotisation }}</strong>
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ $cotisation->adherent->full_name }}</strong>
                                        <br><small class="text-muted">{{ $cotisation->adherent->numero_adherent }}</small>
                                    </div>
                                </td>
                                <td>
                                    {{ $cotisation->periode_cotisation }}
                                    <br><small class="text-muted">{{ $cotisation->annee_cotisation }}</small>
                                </td>
                                <td>
                                    <strong>{{ $cotisation->formatted_montant }}</strong>
                                    @if($cotisation->date_paiement)
                                        <br><small class="text-success">Payé le {{ $cotisation->date_paiement->format('d/m/Y') }}</small>
                                    @endif
                                </td>
                                <td>
                                    {{ $cotisation->date_echeance?->format('d/m/Y') }}
                                    @if($cotisation->isOverdue() && !$cotisation->isPaid())
                                        <br><small class="text-danger">
                                            <i class="feather icon-alert-triangle"></i> En retard
                                        </small>
                                    @endif
                                </td>
                                <td>
                                    <span class="badge bg-{{ $cotisation->status_color }}">
                                        {{ $cotisation->status_label }}
                                    </span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <a href="{{ route('cotisations.show', $cotisation) }}" 
                                           class="btn btn-primary" title="Voir">
                                            <i class="feather icon-eye"></i>
                                        </a>
                                        @if(!$cotisation->isPaid())
                                        <a href="{{ route('cotisations.edit', $cotisation) }}" 
                                           class="btn btn-warning" title="Modifier">
                                            <i class="feather icon-edit"></i>
                                        </a>
                                        <button class="btn btn-success" 
                                                onclick="markAsPaid({{ $cotisation->id }})" 
                                                title="Marquer comme payée">
                                            <i class="feather icon-check"></i>
                                        </button>
                                        @endif
                                        @if($cotisation->isOverdue() && !$cotisation->isPaid())
                                        <button class="btn btn-info" 
                                                onclick="sendReminder({{ $cotisation->id }})" 
                                                title="Envoyer relance">
                                            <i class="feather icon-mail"></i>
                                        </button>
                                        @endif
                                        <button class="btn btn-secondary" 
                                                onclick="printCotisation({{ $cotisation->id }})" 
                                                title="Imprimer">
                                            <i class="feather icon-printer"></i>
                                        </button>
                                        @if(!$cotisation->isPaid())
                                        <button class="btn btn-danger" 
                                                onclick="deleteCotisation({{ $cotisation->id }})" 
                                                title="Supprimer">
                                            <i class="feather icon-trash"></i>
                                        </button>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <div class="row">
                    <div class="col-sm-12 col-md-5">
                        <div class="dataTables_info">
                            Affichage de {{ $cotisations->firstItem() }} à {{ $cotisations->lastItem() }} 
                            sur {{ $cotisations->total() }} entrées
                        </div>
                    </div>
                    <div class="col-sm-12 col-md-7">
                        {{ $cotisations->appends($filters)->links() }}
                    </div>
                </div>
                @else
                <div class="text-center py-4">
                    <i class="feather icon-credit-card f-40 text-muted"></i>
                    <h5 class="mt-3">Aucune cotisation trouvée</h5>
                    <p class="text-muted">Aucune cotisation ne correspond aux critères de recherche.</p>
                    <a href="{{ route('cotisations.create') }}" class="btn btn-primary">
                        <i class="feather icon-plus me-2"></i>Créer la première cotisation
                    </a>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Marquer comme payée</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="paymentForm" method="POST">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group">
                        <label>Mode de paiement</label>
                        <select name="mode_paiement" class="form-control" required>
                            <option value="">Sélectionner</option>
                            <option value="especes">Espèces</option>
                            <option value="cheque">Chèque</option>
                            <option value="virement">Virement bancaire</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="prelevement">Prélèvement automatique</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Référence de paiement (optionnel)</label>
                        <input type="text" name="reference_paiement" class="form-control" 
                               placeholder="Numéro de chèque, référence virement...">
                    </div>
                    <div class="form-group">
                        <label>Date de paiement</label>
                        <input type="date" name="date_paiement" class="form-control" 
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Confirmer le paiement</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function markAsPaid(cotisationId) {
    const form = document.getElementById('paymentForm');
    form.action = `/cotisations/${cotisationId}/pay`;
    
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function sendReminder(cotisationId) {
    if (confirm('Envoyer une relance pour cette cotisation ?')) {
        fetch(`/cotisations/${cotisationId}/reminder`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast('Relance envoyée avec succès', 'success');
            } else {
                CRFM.showToast('Erreur lors de l\'envoi de la relance', 'error');
            }
        });
    }
}

function printCotisation(cotisationId) {
    window.open(`/cotisations/${cotisationId}/print`, '_blank');
}

function deleteCotisation(cotisationId) {
    if (confirm('Êtes-vous sûr de vouloir supprimer cette cotisation ? Cette action est irréversible.')) {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = `/cotisations/${cotisationId}`;
        form.innerHTML = `
            @csrf
            @method('DELETE')
        `;
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
@endpush
