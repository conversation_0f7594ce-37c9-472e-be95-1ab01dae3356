<?php

namespace Modules\Auth\Controllers;

use App\Http\Controllers\BaseController;
use App\Models\User;
use Modules\Auth\Models\Role;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Hash;

class UserController extends BaseController
{
    /**
     * Display a listing of users
     */
    public function index(Request $request): View
    {
        $query = User::with(['roles']);

        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'LIKE', "%{$search}%")
                  ->orWhere('email', 'LIKE', "%{$search}%");
            });
        }

        if ($request->has('role')) {
            $query->whereHas('roles', function ($q) use ($request) {
                $q->where('name', $request->role);
            });
        }

        $users = $query->paginate(15);
        $roles = Role::all();

        return view('auth::admin.users.index', compact('users', 'roles'));
    }

    /**
     * Show the form for creating a new user
     */
    public function create(): View
    {
        $roles = Role::all();
        return view('auth::admin.users.create', compact('roles'));
    }

    /**
     * Store a newly created user
     */
    public function store(Request $request): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);

        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
        ]);

        $user->roles()->sync($request->roles);

        return $this->successRedirect('auth.admin.users.index', 'Utilisateur créé avec succès !');
    }

    /**
     * Display the specified user
     */
    public function show(User $user): View
    {
        $user->load(['roles.permissions']);
        return view('auth::admin.users.show', compact('user'));
    }

    /**
     * Show the form for editing the specified user
     */
    public function edit(User $user): View
    {
        $roles = Role::all();
        $user->load('roles');
        return view('auth::admin.users.edit', compact('user', 'roles'));
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'password' => 'nullable|string|min:8|confirmed',
            'roles' => 'required|array',
            'roles.*' => 'exists:roles,id',
        ]);

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'password' => $request->password ? Hash::make($request->password) : $user->password,
        ]);

        $user->roles()->sync($request->roles);

        return $this->successRedirect('auth.admin.users.index', 'Utilisateur mis à jour avec succès !');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): RedirectResponse
    {
        if ($user->id === auth()->id()) {
            return $this->backWithError('Vous ne pouvez pas supprimer votre propre compte !');
        }

        $user->delete();

        return $this->successRedirect('auth.admin.users.index', 'Utilisateur supprimé avec succès !');
    }

    /**
     * Show user profile
     */
    public function profile(): View
    {
        $user = auth()->user();
        $user->load(['roles.permissions']);
        return view('auth::profile', compact('user'));
    }

    /**
     * Update user profile
     */
    public function updateProfile(Request $request): RedirectResponse
    {
        $user = auth()->user();

        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'current_password' => 'required_with:password',
            'password' => 'nullable|string|min:8|confirmed',
        ]);

        // Verify current password if new password is provided
        if ($request->password) {
            if (!Hash::check($request->current_password, $user->password)) {
                return $this->backWithError('Le mot de passe actuel est incorrect.');
            }
        }

        $user->update([
            'name' => $request->name,
            'email' => $request->email,
            'password' => $request->password ? Hash::make($request->password) : $user->password,
        ]);

        return $this->backWithSuccess('Profil mis à jour avec succès !');
    }
}
