<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('adherents', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->string('numero_adherent')->unique();

            // Informations personnelles
            $table->string('nom');
            $table->string('prenoms');
            $table->date('date_naissance');
            $table->string('lieu_naissance');
            $table->enum('sexe', ['M', 'F']);
            $table->enum('situation_matrimoniale', ['celibataire', 'marie', 'divorce', 'veuf']);
            $table->string('nationalite')->default('Ivoirienne');

            // Informations professionnelles
            $table->string('profession');
            $table->string('employeur');
            $table->date('date_embauche')->nullable();
            $table->decimal('salaire_base', 15, 2)->nullable();

            // Contacts
            $table->string('telephone');
            $table->string('email')->nullable();
            $table->text('adresse_domicile');
            $table->text('adresse_professionnelle')->nullable();
            $table->string('contact_urgence_nom')->nullable();
            $table->string('contact_urgence_telephone')->nullable();

            // Documents d'identité
            $table->string('numero_cni')->nullable();
            $table->date('date_delivrance_cni')->nullable();
            $table->string('lieu_delivrance_cni')->nullable();
            $table->string('numero_passeport')->nullable();

            // Informations adhésion
            $table->date('date_adhesion');
            $table->enum('statut', ['actif', 'suspendu', 'radie', 'retraite'])->default('actif');
            $table->text('observations')->nullable();
            $table->boolean('is_active')->default(true);

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['statut', 'is_active']);
            $table->index(['employeur']);
            $table->index(['date_naissance']);
            $table->index(['date_adhesion']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('adherents');
    }
};
