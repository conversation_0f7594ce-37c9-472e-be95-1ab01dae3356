<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Notification;
use App\Services\NotificationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class NotificationApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected User $testUser;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Créer un utilisateur de test
        $this->testUser = User::factory()->create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'role' => 'admin'
        ]);
    }

    /** @test */
    public function it_can_get_unread_notifications()
    {
        // Créer quelques notifications
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification 1',
            'message' => 'This is test notification 1',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification 2',
            'message' => 'This is test notification 2',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        // Tester l'API
        $response = $this->actingAs($this->testUser)
            ->getJson('/api/notifications/unread');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'notifications' => [
                        '*' => [
                            'id',
                            'title',
                            'message',
                            'type',
                            'type_icon',
                            'priority_color',
                            'time_ago',
                            'created_at'
                        ]
                    ],
                    'count'
                ]
            ]);

        $this->assertEquals(2, $response->json('data.count'));
    }

    /** @test */
    public function it_can_mark_notification_as_read()
    {
        // Créer une notification
        $notificationService = app(NotificationService::class);
        
        $notification = $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        // Vérifier qu'elle n'est pas lue
        $this->assertNull($notification->fresh()->read_at);

        // Marquer comme lue via l'API
        $response = $this->actingAs($this->testUser)
            ->postJson("/api/notifications/{$notification->id}/read");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);

        // Vérifier qu'elle est maintenant lue
        $this->assertNotNull($notification->fresh()->read_at);
    }

    /** @test */
    public function it_can_mark_all_notifications_as_read()
    {
        // Créer plusieurs notifications
        $notificationService = app(NotificationService::class);
        
        for ($i = 1; $i <= 3; $i++) {
            $notificationService->sendToUser($this->testUser, [
                'type' => Notification::TYPE_INFO_GENERALE,
                'title' => "Test Notification {$i}",
                'message' => "This is test notification {$i}",
                'channels' => [Notification::CHANNEL_DATABASE]
            ]);
        }

        // Vérifier qu'elles ne sont pas lues
        $unreadCount = Notification::where('user_id', $this->testUser->id)->unread()->count();
        $this->assertEquals(3, $unreadCount);

        // Marquer toutes comme lues via l'API
        $response = $this->actingAs($this->testUser)
            ->postJson('/api/notifications/mark-all-read');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);

        // Vérifier qu'elles sont toutes lues
        $unreadCount = Notification::where('user_id', $this->testUser->id)->unread()->count();
        $this->assertEquals(0, $unreadCount);
    }

    /** @test */
    public function it_can_delete_notification()
    {
        // Créer une notification
        $notificationService = app(NotificationService::class);
        
        $notification = $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test Notification',
            'message' => 'This is a test notification',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        // Supprimer via l'API
        $response = $this->actingAs($this->testUser)
            ->deleteJson("/api/notifications/{$notification->id}");

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ]);

        // Vérifier qu'elle est supprimée
        $this->assertDatabaseMissing('notifications', ['id' => $notification->id]);
    }

    /** @test */
    public function it_can_send_notification()
    {
        $data = [
            'user_id' => $this->testUser->id,
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'API Test Notification',
            'message' => 'This notification was sent via API',
            'channels' => ['database'],
            'priority' => 'normal'
        ];

        $response = $this->actingAs($this->testUser)
            ->postJson('/api/notifications/send', $data);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'notification_id'
                ]
            ]);

        // Vérifier que la notification a été créée
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->testUser->id,
            'title' => 'API Test Notification',
            'type' => Notification::TYPE_INFO_GENERALE
        ]);
    }

    /** @test */
    public function it_can_get_statistics()
    {
        // Créer quelques notifications
        $notificationService = app(NotificationService::class);
        
        $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Test 1',
            'message' => 'Message 1',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $notificationService->sendToUser($this->testUser, [
            'type' => Notification::TYPE_COTISATION_RAPPEL,
            'title' => 'Test 2',
            'message' => 'Message 2',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        $response = $this->actingAs($this->testUser)
            ->getJson('/api/notifications/statistics');

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'total',
                    'unread',
                    'sent_today',
                    'failed',
                    'by_type'
                ]
            ]);

        $data = $response->json('data');
        $this->assertEquals(2, $data['total']);
        $this->assertEquals(2, $data['unread']);
    }

    /** @test */
    public function it_requires_authentication_for_api_routes()
    {
        $response = $this->getJson('/api/notifications/unread');
        $response->assertStatus(401);

        $response = $this->postJson('/api/notifications/mark-all-read');
        $response->assertStatus(401);

        $response = $this->getJson('/api/notifications/statistics');
        $response->assertStatus(401);
    }

    /** @test */
    public function it_prevents_access_to_other_users_notifications()
    {
        // Créer un autre utilisateur
        $otherUser = User::factory()->create([
            'email' => '<EMAIL>'
        ]);

        // Créer une notification pour l'autre utilisateur
        $notificationService = app(NotificationService::class);
        $notification = $notificationService->sendToUser($otherUser, [
            'type' => Notification::TYPE_INFO_GENERALE,
            'title' => 'Other User Notification',
            'message' => 'This belongs to another user',
            'channels' => [Notification::CHANNEL_DATABASE]
        ]);

        // Essayer d'accéder à la notification d'un autre utilisateur
        $response = $this->actingAs($this->testUser)
            ->postJson("/api/notifications/{$notification->id}/read");

        $response->assertStatus(404);

        $response = $this->actingAs($this->testUser)
            ->deleteJson("/api/notifications/{$notification->id}");

        $response->assertStatus(404);
    }

    /** @test */
    public function it_can_test_notification_system()
    {
        $response = $this->actingAs($this->testUser)
            ->postJson('/api/notifications/test', [
                'channel' => 'database'
            ]);

        $response->assertStatus(200)
            ->assertJson([
                'success' => true
            ])
            ->assertJsonStructure([
                'success',
                'message',
                'data' => [
                    'notification_id',
                    'channel'
                ]
            ]);

        // Vérifier que la notification de test a été créée
        $this->assertDatabaseHas('notifications', [
            'user_id' => $this->testUser->id,
            'type' => Notification::TYPE_SYSTEM_ALERT,
            'title' => 'Test de notification'
        ]);
    }
}
