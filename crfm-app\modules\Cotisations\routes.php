<?php

use Illuminate\Support\Facades\Route;
use Modules\Cotisations\Controllers\CotisationController;

/*
|--------------------------------------------------------------------------
| Cotisations Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes de base (temporairement sans permissions)
    Route::get('/cotisations', [CotisationController::class, 'index'])->name('cotisations.index');
    Route::get('/cotisations/create', [CotisationController::class, 'create'])->name('cotisations.create');
    Route::post('/cotisations', [CotisationController::class, 'store'])->name('cotisations.store');
    Route::get('/cotisations/{cotisation}', [CotisationController::class, 'show'])->name('cotisations.show');
    Route::get('/cotisations/{cotisation}/edit', [CotisationController::class, 'edit'])->name('cotisations.edit');
    Route::put('/cotisations/{cotisation}', [CotisationController::class, 'update'])->name('cotisations.update');
    Route::delete('/cotisations/{cotisation}', [CotisationController::class, 'destroy'])->name('cotisations.destroy');

    // Routes spécialisées
    Route::get('/cotisations/{cotisation}/data', [CotisationController::class, 'getCotisationData'])->name('cotisations.data');
    Route::get('/cotisations/export', [CotisationController::class, 'export'])->name('cotisations.export');
    Route::get('/cotisations/overdue/list', [CotisationController::class, 'overdue'])->name('cotisations.overdue');
    Route::get('/cotisations/{cotisation}/print', [CotisationController::class, 'print'])->name('cotisations.print');

    // Routes de paiement
    Route::post('/cotisations/{cotisation}/pay', [CotisationController::class, 'markAsPaid'])->name('cotisations.pay');

    // API Routes
    Route::get('/api/cotisations/dashboard-stats', [CotisationController::class, 'getDashboardStats'])->name('cotisations.api.dashboard-stats');
    Route::post('/api/cotisations/{cotisation}/reminder', [CotisationController::class, 'sendReminder'])->name('cotisations.api.reminder');

    // Payment management
    Route::patch('/cotisations/{cotisation}/mark-paid', [CotisationController::class, 'markAsPaid'])->name('cotisations.mark-paid');
    Route::patch('/cotisations/{cotisation}/cancel', [CotisationController::class, 'cancel'])->name('cotisations.cancel');

    // Generation and bulk operations
    Route::get('/cotisations/generate/form', [CotisationController::class, 'showGenerate'])->name('cotisations.generate.form');
    Route::post('/cotisations/generate', [CotisationController::class, 'generate'])->name('cotisations.generate');
    Route::post('/cotisations/process-relances', [CotisationController::class, 'processRelances'])->name('cotisations.process-relances');

    // Import/Export
    Route::get('/cotisations/import/form', [CotisationController::class, 'showImport'])->name('cotisations.import.form');
    Route::post('/cotisations/import', [CotisationController::class, 'import'])->name('cotisations.import');

});
