<?php

namespace Modules\Adherents\Controllers;

use App\Http\Controllers\BaseController;
use Modules\Adherents\Models\Adherent;
use Modules\Adherents\Services\AdherentService;
use Modules\Adherents\Requests\CreateAdherentRequest;
use Modules\Adherents\Requests\UpdateAdherentRequest;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;

class AdherentController extends BaseController
{
    protected AdherentService $adherentService;

    public function __construct(AdherentService $adherentService)
    {
        $this->adherentService = $adherentService;
    }

    /**
     * Display a listing of adherents
     */
    public function index(Request $request): View
    {
        $filters = $request->only(['search', 'statut', 'sexe', 'employeur', 'date_adhesion_from', 'date_adhesion_to']);
        $adherents = $this->adherentService->searchAdherents($filters);
        $statistics = $this->adherentService->getStatistics();

        return view('adherents::index', compact('adherents', 'statistics', 'filters'));
    }

    /**
     * Show the form for creating a new adherent
     */
    public function create(): View
    {
        return view('adherents::create');
    }

    /**
     * Store a newly created adherent
     */
    public function store(CreateAdherentRequest $request): RedirectResponse
    {
        try {
            $adherent = $this->adherentService->createAdherent($request->validated());
            
            return redirect()->route('adherents.show', $adherent)
                        ->with('success', 'Adhérent créé avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la création de l\'adhérent : ' . $e->getMessage());
        }
    }

    /**
     * Display the specified adherent
     */
    public function show(Adherent $adherent): View
    {
        $adherent->load(['documents', 'beneficiaires', 'cotisations']);
        
        return view('adherents::show', compact('adherent'));
    }

    /**
     * Show the form for editing the specified adherent
     */
    public function edit(Adherent $adherent): View
    {
        $adherent->load(['beneficiaires']);
        
        return view('adherents::edit', compact('adherent'));
    }

    /**
     * Update the specified adherent
     */
    public function update(UpdateAdherentRequest $request, Adherent $adherent): RedirectResponse
    {
        try {
            $this->adherentService->updateAdherent($adherent, $request->validated());
            
            return redirect()->route('adherents.show', $adherent)
                        ->with('success', 'Adhérent mis à jour avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la mise à jour : ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified adherent
     */
    public function destroy(Adherent $adherent): RedirectResponse
    {
        try {
            $adherent->delete();
            
            return redirect()->route('adherents.index')
                        ->with('success', 'Adhérent supprimé avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la suppression : ' . $e->getMessage());
        }
    }

    /**
     * Suspend adherent
     */
    public function suspend(Request $request, Adherent $adherent): RedirectResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500'
        ]);

        try {
            $this->adherentService->suspendAdherent($adherent, $request->reason);
            
            return back()->with('success', 'Adhérent suspendu avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la suspension : ' . $e->getMessage());
        }
    }

    /**
     * Reactivate adherent
     */
    public function reactivate(Adherent $adherent): RedirectResponse
    {
        try {
            $this->adherentService->reactivateAdherent($adherent);
            
            return back()->with('success', 'Adhérent réactivé avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de la réactivation : ' . $e->getMessage());
        }
    }

    /**
     * Upload document for adherent
     */
    public function uploadDocument(Request $request, Adherent $adherent): RedirectResponse
    {
        $request->validate([
            'document' => 'required|file|mimes:pdf,jpg,jpeg,png|max:5120', // 5MB max
            'type' => 'required|string|in:cni,passeport,acte_naissance,certificat_travail,bulletin_salaire,photo,autre',
            'description' => 'nullable|string|max:255'
        ]);

        try {
            $this->adherentService->uploadDocument(
                $adherent,
                $request->file('document'),
                $request->type,
                $request->description
            );
            
            return back()->with('success', 'Document téléchargé avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors du téléchargement : ' . $e->getMessage());
        }
    }

    /**
     * Export adherents
     */
    public function export(Request $request)
    {
        $filters = $request->only(['search', 'statut', 'sexe', 'employeur', 'date_adhesion_from', 'date_adhesion_to']);
        $data = $this->adherentService->exportAdherents($filters);

        $filename = 'adherents_' . date('Y-m-d_H-i-s') . '.csv';
        
        $headers = [
            'Content-Type' => 'text/csv',
            'Content-Disposition' => "attachment; filename=\"{$filename}\"",
        ];

        $callback = function() use ($data) {
            $file = fopen('php://output', 'w');
            
            // Add BOM for UTF-8
            fwrite($file, "\xEF\xBB\xBF");
            
            // Add headers
            if (!empty($data)) {
                fputcsv($file, array_keys($data[0]), ';');
                
                // Add data
                foreach ($data as $row) {
                    fputcsv($file, $row, ';');
                }
            }
            
            fclose($file);
        };

        return response()->stream($callback, 200, $headers);
    }

    /**
     * Show import form
     */
    public function showImport(): View
    {
        return view('adherents::import');
    }

    /**
     * Import adherents from file
     */
    public function import(Request $request): RedirectResponse
    {
        $request->validate([
            'file' => 'required|file|mimes:csv,xlsx|max:10240' // 10MB max
        ]);

        try {
            // Process file and import data
            // This would require additional CSV/Excel processing logic
            
            return redirect()->route('adherents.index')
                        ->with('success', 'Import réalisé avec succès !');
        } catch (\Exception $e) {
            return back()->with('error', 'Erreur lors de l\'import : ' . $e->getMessage());
        }
    }

    /**
     * Get adherent data for AJAX
     */
    public function getAdherentData(Adherent $adherent)
    {
        $adherent->load(['documents', 'beneficiaires']);
        
        return $this->successResponse('Données récupérées', [
            'adherent' => $adherent,
            'statistics' => [
                'total_cotisations' => $adherent->cotisations()->sum('montant'),
                'derniere_cotisation' => $adherent->cotisations()->latest()->first(),
                'documents_count' => $adherent->documents()->count(),
                'beneficiaires_count' => $adherent->beneficiaires()->count(),
            ]
        ]);
    }
}
