@extends('layouts.template')

@section('title', 'Nouveau Dossier Pension - CRFM')

@section('page-header')
@section('page-title', 'Nouveau Dossier de Pension')
@section('page-description', 'Créer un nouveau dossier de pré-liquidation de pension')
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('pensions.index') }}">Pensions</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">Nouveau</a></li>
    </ul>
@endsection
@endsection

@section('content')
<form method="POST" action="{{ route('pensions.store') }}" class="needs-validation" novalidate>
    @csrf
    
    <div class="row">
        <!-- Informations principales -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-user text-c-blue me-2"></i>Informations du bénéficiaire</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Adhérent</label>
                                <select name="adherent_id" class="form-control @if($errors->has('adherent_id')) is-invalid @endif" required>
                                    <option value="">Sélectionner un adhérent</option>
                                    @foreach($adherents ?? [] as $adherent)
                                        <option value="{{ $adherent->id }}" {{ old('adherent_id') == $adherent->id ? 'selected' : '' }}>
                                            {{ $adherent->full_name }} - {{ $adherent->numero_adherent }}
                                        </option>
                                    @endforeach
                                </select>
                                @if($errors->has('adherent_id'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Numéro de dossier</label>
                                <input type="text" class="form-control" value="Généré automatiquement" readonly>
                                <small class="text-muted">Le numéro sera généré automatiquement</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Type de pension</label>
                                <select name="type_pension" class="form-control @if($errors->has('type_pension')) is-invalid @endif" required>
                                    <option value="">Sélectionner</option>
                                    <option value="vieillesse" {{ old('type_pension') === 'vieillesse' ? 'selected' : '' }}>Vieillesse</option>
                                    <option value="anticipee" {{ old('type_pension') === 'anticipee' ? 'selected' : '' }}>Anticipée</option>
                                    <option value="invalidite" {{ old('type_pension') === 'invalidite' ? 'selected' : '' }}>Invalidité</option>
                                    <option value="survivant" {{ old('type_pension') === 'survivant' ? 'selected' : '' }}>Survivant</option>
                                </select>
                                @if($errors->has('type_pension'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="required">Date de départ à la retraite</label>
                                <input type="date" name="date_depart_retraite" class="form-control @if($errors->has('date_depart_retraite')) is-invalid @endif" 
                                       value="{{ old('date_depart_retraite') }}" required>
                                @if($errors->has('date_depart_retraite'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label>Date de demande</label>
                                <input type="date" name="date_demande" class="form-control @if($errors->has('date_demande')) is-invalid @endif" 
                                       value="{{ old('date_demande', date('Y-m-d')) }}">
                                @if($errors->has('date_demande'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Salaire de référence (€)</label>
                                <input type="number" name="salaire_reference" class="form-control @if($errors->has('salaire_reference')) is-invalid @endif" 
                                       value="{{ old('salaire_reference') }}" min="0" step="1000">
                                @if($errors->has('salaire_reference'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Durée de cotisation (années)</label>
                                <input type="number" name="duree_cotisation" class="form-control @if($errors->has('duree_cotisation')) is-invalid @endif" 
                                       value="{{ old('duree_cotisation') }}" min="0" max="50">
                                @if($errors->has('duree_cotisation'))
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @endif
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Observations</label>
                        <textarea name="observations" class="form-control @if($errors->has('observations')) is-invalid @endif" 
                                  rows="3">{{ old('observations') }}</textarea>
                        @if($errors->has('observations'))
                            <div class="invalid-feedback">{{ $message }}</div>
                        @endif
                    </div>
                </div>
            </div>
            
            <!-- Documents requis -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-file-text text-c-green me-2"></i>Documents requis</h5>
                </div>
                <div class="card-block">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Documents obligatoires :</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="acte_naissance" id="doc1">
                                <label class="form-check-label" for="doc1">Acte de naissance</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="cni" id="doc2">
                                <label class="form-check-label" for="doc2">Carte nationale d'identité</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="certificat_travail" id="doc3">
                                <label class="form-check-label" for="doc3">Certificat de travail</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="bulletin_salaire" id="doc4">
                                <label class="form-check-label" for="doc4">Derniers bulletins de salaire</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <h6>Documents complémentaires :</h6>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="certificat_medical" id="doc5">
                                <label class="form-check-label" for="doc5">Certificat médical (si invalidité)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="acte_deces" id="doc6">
                                <label class="form-check-label" for="doc6">Acte de décès (si survivant)</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="acte_mariage" id="doc7">
                                <label class="form-check-label" for="doc7">Acte de mariage</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="documents[]" value="photo" id="doc8">
                                <label class="form-check-label" for="doc8">Photo d'identité</label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Informations complémentaires -->
        <div class="col-md-4">
            <!-- Calculateur de pension -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-calculator text-c-yellow me-2"></i>Calculateur de pension</h5>
                </div>
                <div class="card-block">
                    <div class="alert alert-info">
                        <i class="feather icon-info me-2"></i>
                        <small>Estimation basée sur les paramètres actuels</small>
                    </div>
                    
                    <div class="form-group">
                        <label>Salaire de base</label>
                        <input type="number" id="calc_salaire" class="form-control" placeholder="Saisir le salaire">
                    </div>
                    
                    <div class="form-group">
                        <label>Années de service</label>
                        <input type="number" id="calc_annees" class="form-control" placeholder="Nombre d'années">
                    </div>
                    
                    <div class="form-group">
                        <label>Type de pension</label>
                        <select id="calc_type" class="form-control">
                            <option value="vieillesse">Vieillesse</option>
                            <option value="anticipee">Anticipée</option>
                            <option value="invalidite">Invalidité</option>
                            <option value="survivant">Survivant</option>
                        </select>
                    </div>
                    
                    <button type="button" class="btn btn-success btn-block" onclick="calculatePension()">
                        <i class="feather icon-calculator me-2"></i>Calculer
                    </button>
                    
                    <div id="calculResult" class="mt-3" style="display: none;">
                        <div class="alert alert-success">
                            <strong>Pension estimée:</strong>
                            <h4 id="montantEstime" class="text-success"></h4>
                            <small>Taux: <span id="tauxCalcule"></span>%</small>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Conditions d'éligibilité -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-check-circle text-c-green me-2"></i>Conditions d'éligibilité</h5>
                </div>
                <div class="card-block">
                    <h6>Pension de vieillesse :</h6>
                    <ul class="list-unstyled">
                        <li><i class="feather icon-check text-success"></i> Âge : 60 ans minimum</li>
                        <li><i class="feather icon-check text-success"></i> Cotisation : 15 ans minimum</li>
                    </ul>
                    
                    <h6>Pension anticipée :</h6>
                    <ul class="list-unstyled">
                        <li><i class="feather icon-check text-warning"></i> Âge : 55 ans minimum</li>
                        <li><i class="feather icon-check text-warning"></i> Cotisation : 20 ans minimum</li>
                        <li><i class="feather icon-info text-info"></i> Réduction de 10%</li>
                    </ul>
                    
                    <h6>Pension d'invalidité :</h6>
                    <ul class="list-unstyled">
                        <li><i class="feather icon-check text-danger"></i> Incapacité ≥ 66%</li>
                        <li><i class="feather icon-check text-danger"></i> Cotisation : 5 ans minimum</li>
                    </ul>
                </div>
            </div>
            
            <!-- Aide -->
            <div class="card">
                <div class="card-header">
                    <h5><i class="feather icon-help-circle text-c-purple me-2"></i>Aide</h5>
                </div>
                <div class="card-block">
                    <h6>Calcul de la pension :</h6>
                    <p class="text-muted small">
                        Pension = Salaire de référence × Taux de liquidation
                        <br><br>
                        Taux = 2% par année de cotisation (max 75%)
                    </p>
                    
                    <h6>Étapes du processus :</h6>
                    <ol class="text-muted small">
                        <li>Réception du dossier</li>
                        <li>Vérification des documents</li>
                        <li>Calcul du montant</li>
                        <li>Validation</li>
                        <li>Liquidation</li>
                        <li>Paiement</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Boutons d'action -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-block text-center">
                    <button type="submit" class="btn btn-primary btn-lg">
                        <i class="feather icon-save me-2"></i>Créer le dossier
                    </button>
                    <a href="{{ route('pensions.index') }}" class="btn btn-secondary btn-lg ms-2">
                        <i class="feather icon-x me-2"></i>Annuler
                    </a>
                </div>
            </div>
        </div>
    </div>
</form>
@endsection

@push('styles')
<style>
.required::after {
    content: " *";
    color: red;
}
</style>
@endpush

@push('scripts')
<script>
// Validation du formulaire
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// Calculateur de pension
function calculatePension() {
    const salaire = parseFloat(document.getElementById('calc_salaire').value);
    const annees = parseFloat(document.getElementById('calc_annees').value);
    const type = document.getElementById('calc_type').value;
    
    if (!salaire || !annees) {
        alert('Veuillez saisir le salaire et les années de service');
        return;
    }
    
    // Calcul du taux de liquidation
    let taux = Math.min(annees * 2, 75); // 2% par année, max 75%
    
    // Ajustements selon le type
    switch(type) {
        case 'anticipee':
            taux *= 0.9; // Réduction de 10%
            break;
        case 'invalidite':
            taux = Math.max(taux, 50); // Minimum 50%
            break;
        case 'survivant':
            taux *= 0.6; // 60% de la pension
            break;
    }
    
    const pension = Math.round(salaire * (taux / 100));
    
    document.getElementById('montantEstime').textContent = pension.toLocaleString() + ' €';
    document.getElementById('tauxCalcule').textContent = taux.toFixed(1);
    document.getElementById('calculResult').style.display = 'block';
    
    // Remplir automatiquement les champs du formulaire
    document.querySelector('input[name="salaire_reference"]').value = salaire;
    document.querySelector('input[name="duree_cotisation"]').value = annees;
}

// Charger les informations de l'adhérent sélectionné
document.querySelector('select[name="adherent_id"]').addEventListener('change', function() {
    const adherentId = this.value;
    if (!adherentId) return;
    
    fetch(`/api/adherents/${adherentId}/pension-info`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Pré-remplir les champs avec les données de l'adhérent
                document.querySelector('input[name="salaire_reference"]').value = data.salaire_base || '';
                document.querySelector('input[name="duree_cotisation"]').value = data.annees_service || '';
                
                // Pré-remplir le calculateur
                document.getElementById('calc_salaire').value = data.salaire_base || '';
                document.getElementById('calc_annees').value = data.annees_service || '';
                
                // Vérifier l'éligibilité
                if (data.eligible_retraite) {
                    CRFM.showToast('Adhérent éligible à la retraite', 'success');
                } else {
                    CRFM.showToast(`Adhérent non éligible (${data.age} ans, ${data.annees_service} ans de service)`, 'warning');
                }
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
        });
});
</script>
@endpush
