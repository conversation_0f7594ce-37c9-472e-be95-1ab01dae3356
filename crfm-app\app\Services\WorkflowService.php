<?php

namespace App\Services;

use App\Models\Notification;
use App\Services\NotificationService;
use Modules\Adherents\Models\Adherent;
use Modules\Cotisations\Models\Cotisation;
use Modules\Pensions\Models\DossierPension;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

class WorkflowService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Traiter les workflows automatiques quotidiens
     */
    public function processDailyWorkflows(): array
    {
        $results = [
            'cotisations_reminders' => 0,
            'cotisations_overdue' => 0,
            'pensions_advanced' => 0,
            'notifications_sent' => 0,
            'errors' => []
        ];

        try {
            // Traiter les rappels de cotisations
            $results['cotisations_reminders'] = $this->processCotisationReminders();
            
            // Traiter les cotisations en retard
            $results['cotisations_overdue'] = $this->processCotisationsOverdue();
            
            // Avancer les dossiers de pension automatiquement
            $results['pensions_advanced'] = $this->processPensionWorkflows();
            
            // Nettoyer les notifications expirées
            $this->notificationService->cleanupExpiredNotifications();
            
        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Erreur dans le workflow quotidien: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Traiter les rappels de cotisations
     */
    protected function processCotisationReminders(): int
    {
        $count = 0;
        
        // Cotisations qui arrivent à échéance dans 7 jours
        $cotisations = Cotisation::where('statut', Cotisation::STATUS_EN_ATTENTE)
            ->whereBetween('date_echeance', [
                now()->addDays(6),
                now()->addDays(8)
            ])
            ->with('adherent')
            ->get();

        foreach ($cotisations as $cotisation) {
            try {
                if ($cotisation->adherent && $cotisation->adherent->email) {
                    $user = \App\Models\User::where('email', $cotisation->adherent->email)->first();
                    if (!$user) {
                        $user = $this->createUserForAdherent($cotisation->adherent);
                    }
                    
                    $this->notificationService->createCotisationReminder($user, $cotisation);
                    $count++;
                }
            } catch (\Exception $e) {
                Log::error("Erreur rappel cotisation {$cotisation->id}: " . $e->getMessage());
            }
        }

        return $count;
    }

    /**
     * Traiter les cotisations en retard
     */
    protected function processCotisationsOverdue(): int
    {
        $count = 0;
        
        // Cotisations en retard
        $cotisations = Cotisation::where('statut', Cotisation::STATUS_EN_ATTENTE)
            ->where('date_echeance', '<', now())
            ->with('adherent')
            ->get();

        foreach ($cotisations as $cotisation) {
            try {
                // Marquer comme en retard si pas déjà fait
                if ($cotisation->statut !== Cotisation::STATUS_EN_RETARD) {
                    $cotisation->update(['statut' => Cotisation::STATUS_EN_RETARD]);
                }

                // Envoyer notification si l'adhérent a un email
                if ($cotisation->adherent && $cotisation->adherent->email) {
                    $user = \App\Models\User::where('email', $cotisation->adherent->email)->first();
                    if (!$user) {
                        $user = $this->createUserForAdherent($cotisation->adherent);
                    }
                    
                    // Vérifier si une relance n'a pas déjà été envoyée récemment
                    $recentNotification = Notification::where('user_id', $user->id)
                        ->where('notifiable_type', get_class($cotisation))
                        ->where('notifiable_id', $cotisation->id)
                        ->where('type', Notification::TYPE_COTISATION_RETARD)
                        ->where('created_at', '>', now()->subDays(7))
                        ->exists();

                    if (!$recentNotification) {
                        $this->notificationService->createCotisationOverdue($user, $cotisation);
                        $count++;
                    }
                }
            } catch (\Exception $e) {
                Log::error("Erreur cotisation en retard {$cotisation->id}: " . $e->getMessage());
            }
        }

        return $count;
    }

    /**
     * Traiter les workflows de pension
     */
    protected function processPensionWorkflows(): int
    {
        $count = 0;
        
        // Dossiers prêts pour l'étape suivante
        $dossiers = DossierPension::where('statut', 'en_cours')
            ->where('updated_at', '<', now()->subDays(3)) // Pas de mise à jour depuis 3 jours
            ->with('adherent')
            ->get();

        foreach ($dossiers as $dossier) {
            try {
                $nextStep = $this->determineNextPensionStep($dossier);
                
                if ($nextStep) {
                    $dossier->update([
                        'etape_actuelle' => $nextStep,
                        'updated_at' => now()
                    ]);

                    // Notifier l'adhérent
                    if ($dossier->adherent && $dossier->adherent->email) {
                        $user = \App\Models\User::where('email', $dossier->adherent->email)->first();
                        if (!$user) {
                            $user = $this->createUserForAdherent($dossier->adherent);
                        }
                        
                        $this->notificationService->createPensionStepNotification($user, $dossier, $nextStep);
                    }
                    
                    $count++;
                }
            } catch (\Exception $e) {
                Log::error("Erreur workflow pension {$dossier->id}: " . $e->getMessage());
            }
        }

        return $count;
    }

    /**
     * Déterminer la prochaine étape pour un dossier de pension
     */
    protected function determineNextPensionStep(DossierPension $dossier): ?string
    {
        $currentStep = $dossier->etape_actuelle;
        
        $workflow = [
            'constitution' => 'verification',
            'verification' => 'calcul',
            'calcul' => 'validation',
            'validation' => 'liquidation',
            'liquidation' => 'paiement'
        ];

        return $workflow[$currentStep] ?? null;
    }

    /**
     * Créer un utilisateur pour un adhérent
     */
    protected function createUserForAdherent(Adherent $adherent): \App\Models\User
    {
        return \App\Models\User::create([
            'name' => $adherent->full_name,
            'email' => $adherent->email,
            'password' => bcrypt('temporary_' . str_random(8)),
            'role' => 'adherent',
            'email_verified_at' => now()
        ]);
    }

    /**
     * Traiter les relances automatiques
     */
    public function processAutomaticReminders(): array
    {
        $results = [
            'cotisations_sent' => 0,
            'pensions_sent' => 0,
            'errors' => []
        ];

        try {
            // Relances pour cotisations en retard (tous les 15 jours)
            $overdueDate = now()->subDays(15);
            $cotisations = Cotisation::where('statut', Cotisation::STATUS_EN_RETARD)
                ->where('date_echeance', '<', $overdueDate)
                ->whereDoesntHave('relances', function($query) {
                    $query->where('created_at', '>', now()->subDays(15));
                })
                ->with('adherent')
                ->get();

            foreach ($cotisations as $cotisation) {
                try {
                    if ($cotisation->adherent && $cotisation->adherent->email) {
                        $user = \App\Models\User::where('email', $cotisation->adherent->email)->first();
                        if ($user) {
                            $this->notificationService->createCotisationOverdue($user, $cotisation);
                            $results['cotisations_sent']++;
                        }
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Cotisation {$cotisation->id}: " . $e->getMessage();
                }
            }

            // Relances pour dossiers de pension en attente
            $pensionDossiers = DossierPension::where('statut', 'en_cours')
                ->where('updated_at', '<', now()->subDays(30))
                ->with('adherent')
                ->get();

            foreach ($pensionDossiers as $dossier) {
                try {
                    if ($dossier->adherent && $dossier->adherent->email) {
                        $user = \App\Models\User::where('email', $dossier->adherent->email)->first();
                        if ($user) {
                            $this->notificationService->sendForModel($dossier, [
                                'user' => $user,
                                'type' => Notification::TYPE_PENSION_ETAPE,
                                'title' => 'Dossier de pension en attente',
                                'message' => "Votre dossier de pension {$dossier->numero_dossier} nécessite votre attention",
                                'channels' => ['database', 'email'],
                                'priority' => 'normal'
                            ]);
                            $results['pensions_sent']++;
                        }
                    }
                } catch (\Exception $e) {
                    $results['errors'][] = "Dossier {$dossier->id}: " . $e->getMessage();
                }
            }

        } catch (\Exception $e) {
            $results['errors'][] = $e->getMessage();
            Log::error('Erreur dans les relances automatiques: ' . $e->getMessage());
        }

        return $results;
    }

    /**
     * Générer un rapport de workflow
     */
    public function generateWorkflowReport(): array
    {
        return [
            'cotisations' => [
                'en_attente' => Cotisation::where('statut', Cotisation::STATUS_EN_ATTENTE)->count(),
                'en_retard' => Cotisation::where('statut', Cotisation::STATUS_EN_RETARD)->count(),
                'payees' => Cotisation::where('statut', Cotisation::STATUS_PAYEE)->count(),
            ],
            'pensions' => [
                'en_cours' => DossierPension::where('statut', 'en_cours')->count(),
                'liquides' => DossierPension::where('statut', 'liquide')->count(),
                'rejetes' => DossierPension::where('statut', 'rejete')->count(),
            ],
            'notifications' => [
                'total' => Notification::count(),
                'unread' => Notification::unread()->count(),
                'sent_today' => Notification::whereDate('sent_at', today())->count(),
            ]
        ];
    }
}
