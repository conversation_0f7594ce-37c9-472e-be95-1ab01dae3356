<?php

namespace Modules\Adherents\Repositories;

use App\Repositories\BaseRepository;
use App\Contracts\RepositoryInterface;
use Modules\Adherents\Models\Adherent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Pagination\LengthAwarePaginator;

class AdherentRepository extends BaseRepository implements RepositoryInterface
{
    /**
     * Constructor
     */
    public function __construct(Adherent $model)
    {
        parent::__construct($model);
    }

    /**
     * Get active adherents
     */
    public function getActiveAdherents(): Collection
    {
        return $this->model->active()->get();
    }

    /**
     * Get adherents with relationships
     */
    public function getAdherentsWithRelations(): Collection
    {
        return $this->model->with(['documents', 'beneficiaires'])->get();
    }

    /**
     * Get paginated adherents with search
     */
    public function getPaginatedAdherents(array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $query = $this->model->newQuery();

        // Apply filters
        if (!empty($filters['search'])) {
            $query->search($filters['search']);
        }

        if (!empty($filters['statut'])) {
            $query->byStatus($filters['statut']);
        }

        if (!empty($filters['sexe'])) {
            $query->byGender($filters['sexe']);
        }

        if (!empty($filters['employeur'])) {
            $query->byEmployer($filters['employeur']);
        }

        if (!empty($filters['date_adhesion_from'])) {
            $query->where('date_adhesion', '>=', $filters['date_adhesion_from']);
        }

        if (!empty($filters['date_adhesion_to'])) {
            $query->where('date_adhesion', '<=', $filters['date_adhesion_to']);
        }

        return $query->orderBy('created_at', 'desc')->paginate($perPage);
    }

    /**
     * Find adherent by numero
     */
    public function findByNumero(string $numero): ?Adherent
    {
        return $this->model->where('numero_adherent', $numero)->first();
    }

    /**
     * Get adherents by employer
     */
    public function getByEmployer(string $employer): Collection
    {
        return $this->model->byEmployer($employer)->get();
    }

    /**
     * Get retirement eligible adherents
     */
    public function getRetirementEligible(): Collection
    {
        return $this->model->retirementEligible()->get();
    }

    /**
     * Get adherents statistics
     */
    public function getStatistics(): array
    {
        return [
            'total' => $this->model->count(),
            'actifs' => $this->model->byStatus(Adherent::STATUS_ACTIF)->count(),
            'suspendus' => $this->model->byStatus(Adherent::STATUS_SUSPENDU)->count(),
            'radies' => $this->model->byStatus(Adherent::STATUS_RADIE)->count(),
            'retraites' => $this->model->byStatus(Adherent::STATUS_RETRAITE)->count(),
            'hommes' => $this->model->byGender(Adherent::SEXE_MASCULIN)->count(),
            'femmes' => $this->model->byGender(Adherent::SEXE_FEMININ)->count(),
            'eligibles_retraite' => $this->model->retirementEligible()->count(),
        ];
    }

    /**
     * Get adherents by age range
     */
    public function getByAgeRange(int $minAge, int $maxAge): Collection
    {
        $minDate = now()->subYears($maxAge);
        $maxDate = now()->subYears($minAge);
        
        return $this->model->whereBetween('date_naissance', [$minDate, $maxDate])->get();
    }

    /**
     * Get recent adherents
     */
    public function getRecentAdherents(int $days = 30): Collection
    {
        return $this->model->where('date_adhesion', '>=', now()->subDays($days))
                          ->orderBy('date_adhesion', 'desc')
                          ->get();
    }

    /**
     * Generate next adherent number
     */
    public function generateNextNumero(): string
    {
        $year = date('Y');
        $lastAdherent = $this->model->where('numero_adherent', 'LIKE', "ADH{$year}%")
                                   ->orderBy('numero_adherent', 'desc')
                                   ->first();

        if ($lastAdherent) {
            $lastNumber = (int) substr($lastAdherent->numero_adherent, -4);
            $nextNumber = $lastNumber + 1;
        } else {
            $nextNumber = 1;
        }

        return "ADH{$year}" . str_pad($nextNumber, 4, '0', STR_PAD_LEFT);
    }

    /**
     * Get adherents grouped by employer
     */
    public function getGroupedByEmployer(): array
    {
        return $this->model->selectRaw('employeur, COUNT(*) as count')
                          ->groupBy('employeur')
                          ->orderBy('count', 'desc')
                          ->get()
                          ->toArray();
    }

    /**
     * Search adherents with advanced criteria
     */
    public function advancedSearch(array $criteria, int $perPage = 15)
    {
        $query = $this->model->newQuery();

        foreach ($criteria as $field => $value) {
            if (!empty($value)) {
                switch ($field) {
                    case 'nom':
                    case 'prenoms':
                    case 'telephone':
                    case 'email':
                        $query->where($field, 'LIKE', "%{$value}%");
                        break;
                    case 'age_min':
                        $maxDate = now()->subYears($value);
                        $query->where('date_naissance', '<=', $maxDate);
                        break;
                    case 'age_max':
                        $minDate = now()->subYears($value);
                        $query->where('date_naissance', '>=', $minDate);
                        break;
                    default:
                        if (in_array($field, $this->model->getFillable())) {
                            $query->where($field, $value);
                        }
                        break;
                }
            }
        }

        return $query->paginate($perPage);
    }
}
