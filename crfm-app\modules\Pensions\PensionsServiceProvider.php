<?php

namespace Modules\Pensions;

use App\Providers\BaseModuleServiceProvider;

class PensionsServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "Pensions";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register repositories
        $this->app->bind(\Modules\Pensions\Repositories\DossierPensionRepository::class, function ($app) {
            return new \Modules\Pensions\Repositories\DossierPensionRepository(new \Modules\Pensions\Models\DossierPension());
        });

        // Register services
        $this->app->bind(\Modules\Pensions\Services\PensionService::class);
    }
}