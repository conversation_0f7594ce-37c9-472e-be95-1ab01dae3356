@extends('layouts.template')

@section('title', 'Détail Cotisation - CRFM')

@section('page-header')
@section('page-title', 'Détail de la cotisation')
@section('page-description', $cotisation->numero_cotisation . ' - ' . $cotisation->periode_cotisation)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('cotisations.index') }}">Cotisations</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">{{ $cotisation->numero_cotisation }}</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Informations principales -->
    <div class="col-md-8">
        <!-- Détails de la cotisation -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-credit-card text-c-blue me-2"></i>Détails de la cotisation</h5>
                <div class="card-header-right">
                    <div class="btn-group">
                        @if(!$cotisation->isPaid())
                        <a href="{{ route('cotisations.edit', $cotisation) }}" class="btn btn-warning btn-sm">
                            <i class="feather icon-edit me-1"></i>Modifier
                        </a>
                        <button class="btn btn-success btn-sm" onclick="markAsPaid()">
                            <i class="feather icon-check me-1"></i>Marquer payée
                        </button>
                        @endif
                        <button class="btn btn-info btn-sm" onclick="window.print()">
                            <i class="feather icon-printer me-1"></i>Imprimer
                        </button>
                        <a href="{{ route('cotisations.pdf', $cotisation) }}" class="btn btn-secondary btn-sm" target="_blank">
                            <i class="feather icon-download me-1"></i>PDF
                        </a>
                    </div>
                </div>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Numéro:</strong></td>
                                <td>{{ $cotisation->numero_cotisation }}</td>
                            </tr>
                            <tr>
                                <td><strong>Adhérent:</strong></td>
                                <td>
                                    <a href="{{ route('adherents.show', $cotisation->adherent) }}">
                                        {{ $cotisation->adherent->full_name }}
                                    </a>
                                    <br><small class="text-muted">{{ $cotisation->adherent->numero_adherent }}</small>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Période:</strong></td>
                                <td>{{ $cotisation->periode_cotisation }} {{ $cotisation->annee_cotisation }}</td>
                            </tr>
                            <tr>
                                <td><strong>Type:</strong></td>
                                <td>{{ ucfirst($cotisation->type_cotisation) }}</td>
                            </tr>
                            <tr>
                                <td><strong>Salaire de référence:</strong></td>
                                <td>{{ $cotisation->formatted_salaire_reference }}</td>
                            </tr>
                            <tr>
                                <td><strong>Taux de cotisation:</strong></td>
                                <td>{{ $cotisation->taux_cotisation }}%</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Montant:</strong></td>
                                <td><h5 class="text-primary">{{ $cotisation->formatted_montant }}</h5></td>
                            </tr>
                            <tr>
                                <td><strong>Date d'échéance:</strong></td>
                                <td>
                                    {{ $cotisation->date_echeance?->format('d/m/Y') }}
                                    @if($cotisation->isOverdue() && !$cotisation->isPaid())
                                        <br><small class="text-danger">
                                            <i class="feather icon-alert-triangle"></i> En retard de {{ $cotisation->days_overdue }} jour(s)
                                        </small>
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $cotisation->status_color }} badge-lg">
                                        {{ $cotisation->status_label }}
                                    </span>
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Date de création:</strong></td>
                                <td>{{ $cotisation->created_at->format('d/m/Y à H:i') }}</td>
                            </tr>
                            @if($cotisation->date_paiement)
                            <tr>
                                <td><strong>Date de paiement:</strong></td>
                                <td>{{ $cotisation->date_paiement->format('d/m/Y à H:i') }}</td>
                            </tr>
                            @endif
                            @if($cotisation->mode_paiement)
                            <tr>
                                <td><strong>Mode de paiement:</strong></td>
                                <td>{{ ucfirst(str_replace('_', ' ', $cotisation->mode_paiement)) }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                </div>
                
                @if($cotisation->reference_paiement)
                <div class="alert alert-success">
                    <strong>Référence de paiement:</strong> {{ $cotisation->reference_paiement }}
                </div>
                @endif
                
                @if($cotisation->observations)
                <div class="alert alert-info">
                    <strong>Observations:</strong> {{ $cotisation->observations }}
                </div>
                @endif
            </div>
        </div>
        
        <!-- Informations de l'adhérent -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-user text-c-green me-2"></i>Informations de l'adhérent</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Nom complet:</strong></td>
                                <td>{{ $cotisation->adherent->full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Profession:</strong></td>
                                <td>{{ $cotisation->adherent->profession }}</td>
                            </tr>
                            <tr>
                                <td><strong>Employeur:</strong></td>
                                <td>{{ $cotisation->adherent->employeur }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Téléphone:</strong></td>
                                <td>{{ $cotisation->adherent->telephone ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $cotisation->adherent->email ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $cotisation->adherent->status_color }}">
                                        {{ $cotisation->adherent->status_label }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historique des paiements -->
        @if($cotisation->paiements->count() > 0)
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-list text-c-yellow me-2"></i>Historique des paiements</h5>
            </div>
            <div class="card-block">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Montant</th>
                                <th>Mode</th>
                                <th>Référence</th>
                                <th>Utilisateur</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($cotisation->paiements as $paiement)
                            <tr>
                                <td>{{ $paiement->date_paiement->format('d/m/Y à H:i') }}</td>
                                <td>{{ $paiement->formatted_montant }}</td>
                                <td>{{ ucfirst(str_replace('_', ' ', $paiement->mode_paiement)) }}</td>
                                <td>{{ $paiement->reference_paiement ?: '-' }}</td>
                                <td>{{ $paiement->user->name }}</td>
                            </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        @endif
    </div>
    
    <!-- Sidebar avec actions et informations -->
    <div class="col-md-4">
        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-red me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="d-grid gap-2">
                    @if(!$cotisation->isPaid())
                    <button class="btn btn-success" onclick="markAsPaid()">
                        <i class="feather icon-check me-2"></i>Marquer comme payée
                    </button>
                    @endif
                    
                    @if($cotisation->isOverdue() && !$cotisation->isPaid())
                    <button class="btn btn-warning" onclick="sendReminder()">
                        <i class="feather icon-mail me-2"></i>Envoyer relance
                    </button>
                    @endif
                    
                    <a href="{{ route('cotisations.pdf', $cotisation) }}" class="btn btn-info" target="_blank">
                        <i class="feather icon-download me-2"></i>Télécharger PDF
                    </a>
                    
                    <button class="btn btn-secondary" onclick="window.print()">
                        <i class="feather icon-printer me-2"></i>Imprimer
                    </button>
                    
                    @if($cotisation->adherent->email)
                    <button class="btn btn-outline-primary" onclick="sendByEmail()">
                        <i class="feather icon-send me-2"></i>Envoyer par email
                    </button>
                    @endif
                    
                    @if(!$cotisation->isPaid())
                    <a href="{{ route('cotisations.edit', $cotisation) }}" class="btn btn-outline-warning">
                        <i class="feather icon-edit me-2"></i>Modifier
                    </a>
                    @endif
                </div>
            </div>
        </div>
        
        <!-- Statistiques de l'adhérent -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bar-chart text-c-purple me-2"></i>Statistiques adhérent</h5>
            </div>
            <div class="card-block">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-c-blue">{{ $cotisation->adherent->cotisations->where('statut', 'payee')->count() }}</h4>
                        <p class="text-muted m-b-0">Payées</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-c-red">{{ $cotisation->adherent->cotisations->where('statut', 'en_retard')->count() }}</h4>
                        <p class="text-muted m-b-0">En retard</p>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-12">
                        <h4 class="text-c-green">{{ $cotisation->adherent->total_cotisations_payees }}</h4>
                        <p class="text-muted m-b-0">Total payé (€)</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Cotisations récentes -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-clock text-c-yellow me-2"></i>Cotisations récentes</h5>
            </div>
            <div class="card-block">
                @if($cotisation->adherent->cotisations->take(5)->count() > 0)
                    @foreach($cotisation->adherent->cotisations->take(5) as $recent)
                    <div class="d-flex align-items-center justify-content-between mb-3">
                        <div>
                            <h6 class="mb-1">{{ $recent->periode_cotisation }} {{ $recent->annee_cotisation }}</h6>
                            <small class="text-muted">{{ $recent->formatted_montant }}</small>
                        </div>
                        <span class="badge bg-{{ $recent->status_color }}">
                            {{ $recent->status_label }}
                        </span>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">Aucune cotisation récente</p>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Modal de paiement -->
<div class="modal fade" id="paymentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Marquer comme payée</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('cotisations.pay', $cotisation) }}">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group">
                        <label>Mode de paiement</label>
                        <select name="mode_paiement" class="form-control" required>
                            <option value="">Sélectionner</option>
                            <option value="especes">Espèces</option>
                            <option value="cheque">Chèque</option>
                            <option value="virement">Virement bancaire</option>
                            <option value="mobile_money">Mobile Money</option>
                            <option value="prelevement">Prélèvement automatique</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Référence de paiement (optionnel)</label>
                        <input type="text" name="reference_paiement" class="form-control" 
                               placeholder="Numéro de chèque, référence virement...">
                    </div>
                    <div class="form-group">
                        <label>Date de paiement</label>
                        <input type="date" name="date_paiement" class="form-control" 
                               value="{{ date('Y-m-d') }}" required>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">Confirmer le paiement</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function markAsPaid() {
    const modal = new bootstrap.Modal(document.getElementById('paymentModal'));
    modal.show();
}

function sendReminder() {
    if (confirm('Envoyer une relance pour cette cotisation ?')) {
        fetch(`{{ route('cotisations.reminder', $cotisation) }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast('Relance envoyée avec succès', 'success');
            } else {
                CRFM.showToast('Erreur lors de l\'envoi de la relance', 'error');
            }
        });
    }
}

function sendByEmail() {
    if (confirm('Envoyer cette cotisation par email à l\'adhérent ?')) {
        fetch(`{{ route('cotisations.email', $cotisation) }}`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                CRFM.showToast('Email envoyé avec succès', 'success');
            } else {
                CRFM.showToast('Erreur lors de l\'envoi de l\'email', 'error');
            }
        });
    }
}
</script>
@endpush
