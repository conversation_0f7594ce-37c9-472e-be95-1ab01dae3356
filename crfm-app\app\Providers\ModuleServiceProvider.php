<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\File;

class ModuleServiceProvider extends ServiceProvider
{
    /**
     * Register services.
     */
    public function register(): void
    {
        $this->registerModules();
    }

    /**
     * Bootstrap services.
     */
    public function boot(): void
    {
        $this->bootModules();
    }

    /**
     * Register all modules
     */
    protected function registerModules(): void
    {
        $modulesPath = base_path('modules');
        
        if (!File::exists($modulesPath)) {
            return;
        }

        $modules = File::directories($modulesPath);

        foreach ($modules as $modulePath) {
            $moduleName = basename($modulePath);
            $serviceProviderPath = $modulePath . '/' . $moduleName . 'ServiceProvider.php';
            
            if (File::exists($serviceProviderPath)) {
                $serviceProviderClass = "Modules\\{$moduleName}\\{$moduleName}ServiceProvider";
                
                if (class_exists($serviceProviderClass)) {
                    $this->app->register($serviceProviderClass);
                }
            }
        }
    }

    /**
     * Boot all modules
     */
    protected function bootModules(): void
    {
        $modulesPath = base_path('modules');
        
        if (!File::exists($modulesPath)) {
            return;
        }

        $modules = File::directories($modulesPath);

        foreach ($modules as $modulePath) {
            $moduleName = basename($modulePath);
            
            // Load module routes
            $routesPath = $modulePath . '/routes.php';
            if (File::exists($routesPath)) {
                $this->loadRoutesFrom($routesPath);
            }

            // Load module views
            $viewsPath = $modulePath . '/Views';
            if (File::exists($viewsPath)) {
                $this->loadViewsFrom($viewsPath, strtolower($moduleName));
            }

            // Load module migrations
            $migrationsPath = $modulePath . '/Migrations';
            if (File::exists($migrationsPath)) {
                $this->loadMigrationsFrom($migrationsPath);
            }
        }
    }
}
