@extends('layouts.template')

@section('title', 'Dé<PERSON> Adhérent - CRFM')

@section('page-header')
@section('page-title', '<PERSON><PERSON><PERSON> de l\'adhérent')
@section('page-description', $adherent->full_name . ' - ' . $adherent->numero_adherent)
@section('breadcrumb')
    <ul class="breadcrumb-title">
        <li class="breadcrumb-item">
            <a href="{{ route('dashboard.index') }}"><i class="feather icon-home"></i></a>
        </li>
        <li class="breadcrumb-item">
            <a href="{{ route('adherents.index') }}">Adhérents</a>
        </li>
        <li class="breadcrumb-item"><a href="#!">{{ $adherent->numero_adherent }}</a></li>
    </ul>
@endsection
@endsection

@section('content')
<div class="row">
    <!-- Informations principales -->
    <div class="col-md-8">
        <!-- Profil de l'adhérent -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-user text-c-blue me-2"></i>Profil de l'adhérent</h5>
                <div class="card-header-right">
                    <div class="btn-group">
                        <a href="{{ route('adherents.edit', $adherent) }}" class="btn btn-warning btn-sm">
                            <i class="feather icon-edit me-1"></i>Modifier
                        </a>
                        @if($adherent->statut === 'actif')
                        <button class="btn btn-secondary btn-sm" onclick="suspendAdherent()">
                            <i class="feather icon-pause me-1"></i>Suspendre
                        </button>
                        @elseif($adherent->statut === 'suspendu')
                        <form method="POST" action="{{ route('adherents.reactivate', $adherent) }}" style="display: inline;">
                            @csrf
                            @method('PATCH')
                            <button type="submit" class="btn btn-success btn-sm">
                                <i class="feather icon-play me-1"></i>Réactiver
                            </button>
                        </form>
                        @endif
                        <button class="btn btn-info btn-sm" onclick="window.print()">
                            <i class="feather icon-printer me-1"></i>Imprimer
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Numéro adhérent:</strong></td>
                                <td>{{ $adherent->numero_adherent }}</td>
                            </tr>
                            <tr>
                                <td><strong>Nom complet:</strong></td>
                                <td>{{ $adherent->full_name }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date de naissance:</strong></td>
                                <td>{{ $adherent->date_naissance?->format('d/m/Y') }} ({{ $adherent->age }} ans)</td>
                            </tr>
                            <tr>
                                <td><strong>Lieu de naissance:</strong></td>
                                <td>{{ $adherent->lieu_naissance ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Sexe:</strong></td>
                                <td>{{ $adherent->sexe === 'M' ? 'Masculin' : 'Féminin' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Situation matrimoniale:</strong></td>
                                <td>{{ ucfirst($adherent->situation_matrimoniale ?: 'Non renseigné') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Nationalité:</strong></td>
                                <td>{{ $adherent->nationalite ?: 'Non renseigné' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Téléphone:</strong></td>
                                <td>{{ $adherent->telephone ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Email:</strong></td>
                                <td>{{ $adherent->email ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Adresse domicile:</strong></td>
                                <td>{{ $adherent->adresse_domicile ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Contact d'urgence:</strong></td>
                                <td>
                                    @if($adherent->contact_urgence_nom)
                                        {{ $adherent->contact_urgence_nom }}<br>
                                        <small>{{ $adherent->contact_urgence_telephone }}</small>
                                    @else
                                        Non renseigné
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td><strong>Date d'adhésion:</strong></td>
                                <td>{{ $adherent->date_adhesion?->format('d/m/Y') }}</td>
                            </tr>
                            <tr>
                                <td><strong>Statut:</strong></td>
                                <td>
                                    <span class="badge bg-{{ $adherent->status_color }}">
                                        {{ $adherent->status_label }}
                                    </span>
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
                
                @if($adherent->observations)
                <div class="alert alert-info">
                    <strong>Observations:</strong> {{ $adherent->observations }}
                </div>
                @endif
            </div>
        </div>
        
        <!-- Informations professionnelles -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-briefcase text-c-green me-2"></i>Informations professionnelles</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Profession:</strong></td>
                                <td>{{ $adherent->profession ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Employeur:</strong></td>
                                <td>{{ $adherent->employeur ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date d'embauche:</strong></td>
                                <td>
                                    @if($adherent->date_embauche)
                                        {{ $adherent->date_embauche->format('d/m/Y') }}
                                        ({{ $adherent->years_of_service }} ans de service)
                                    @else
                                        Non renseigné
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td><strong>Salaire de base:</strong></td>
                                <td>{{ $adherent->salaire_base ? $adherent->formatted_salaire : 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Adresse professionnelle:</strong></td>
                                <td>{{ $adherent->adresse_professionnelle ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Éligible à la retraite:</strong></td>
                                <td>
                                    @if($adherent->isRetirementEligible())
                                        <span class="badge bg-success">Oui</span>
                                    @else
                                        <span class="badge bg-secondary">Non ({{ 60 - $adherent->age }} ans restants)</span>
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Pièces d'identité -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-credit-card text-c-yellow me-2"></i>Pièces d'identité</h5>
            </div>
            <div class="card-block">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Carte Nationale d'Identité</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Numéro:</strong></td>
                                <td>{{ $adherent->numero_cni ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Date délivrance:</strong></td>
                                <td>{{ $adherent->date_delivrance_cni?->format('d/m/Y') ?: 'Non renseigné' }}</td>
                            </tr>
                            <tr>
                                <td><strong>Lieu délivrance:</strong></td>
                                <td>{{ $adherent->lieu_delivrance_cni ?: 'Non renseigné' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h6>Passeport</h6>
                        <table class="table table-borderless table-sm">
                            <tr>
                                <td><strong>Numéro:</strong></td>
                                <td>{{ $adherent->numero_passeport ?: 'Non renseigné' }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Sidebar avec statistiques et actions -->
    <div class="col-md-4">
        <!-- Statistiques rapides -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-bar-chart text-c-purple me-2"></i>Statistiques</h5>
            </div>
            <div class="card-block">
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-c-blue">{{ $adherent->cotisations->count() }}</h4>
                        <p class="text-muted m-b-0">Cotisations</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-c-green">{{ $adherent->documents->count() }}</h4>
                        <p class="text-muted m-b-0">Documents</p>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <h4 class="text-c-yellow">{{ $adherent->beneficiaires->count() }}</h4>
                        <p class="text-muted m-b-0">Bénéficiaires</p>
                    </div>
                    <div class="col-6">
                        <h4 class="text-c-red">{{ $adherent->years_of_service }}</h4>
                        <p class="text-muted m-b-0">Années service</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Bénéficiaires -->
        @if($adherent->beneficiaires->count() > 0)
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-users text-c-green me-2"></i>Bénéficiaires</h5>
            </div>
            <div class="card-block">
                @foreach($adherent->beneficiaires as $beneficiaire)
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">{{ $beneficiaire->nom }} {{ $beneficiaire->prenoms }}</h6>
                        <small class="text-muted">{{ $beneficiaire->lien_parente }} - {{ $beneficiaire->pourcentage }}%</small>
                    </div>
                </div>
                @endforeach
            </div>
        </div>
        @endif
        
        <!-- Documents -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-file-text text-c-yellow me-2"></i>Documents</h5>
                <div class="card-header-right">
                    <button class="btn btn-primary btn-sm" data-bs-toggle="modal" data-bs-target="#uploadModal">
                        <i class="feather icon-upload me-1"></i>Ajouter
                    </button>
                </div>
            </div>
            <div class="card-block">
                @if($adherent->documents->count() > 0)
                    @foreach($adherent->documents as $document)
                    <div class="d-flex align-items-center justify-content-between mb-2">
                        <div>
                            <small><strong>{{ ucfirst($document->type) }}</strong></small><br>
                            <small class="text-muted">{{ $document->created_at->format('d/m/Y') }}</small>
                        </div>
                        <div>
                            <a href="{{ $document->file_path }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="feather icon-eye"></i>
                            </a>
                        </div>
                    </div>
                    @endforeach
                @else
                    <p class="text-muted text-center">Aucun document</p>
                @endif
            </div>
        </div>
        
        <!-- Actions rapides -->
        <div class="card">
            <div class="card-header">
                <h5><i class="feather icon-zap text-c-red me-2"></i>Actions rapides</h5>
            </div>
            <div class="card-block">
                <div class="d-grid gap-2">
                    <a href="{{ route('cotisations.create', ['adherent_id' => $adherent->id]) }}" class="btn btn-outline-primary">
                        <i class="feather icon-plus me-2"></i>Nouvelle cotisation
                    </a>
                    <a href="{{ route('pensions.create', ['adherent_id' => $adherent->id]) }}" class="btn btn-outline-success">
                        <i class="feather icon-file-plus me-2"></i>Dossier pension
                    </a>
                    <button class="btn btn-outline-info" onclick="sendNotificationToAdherent('{{ $adherent->id }}')">
                        <i class="feather icon-mail me-2"></i>Envoyer notification
                    </button>
                    <button class="btn btn-outline-warning" onclick="exportAdherentData('{{ $adherent->id }}')">
                        <i class="feather icon-download me-2"></i>Exporter données
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal d'upload de document -->
<div class="modal fade" id="uploadModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Ajouter un document</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('adherents.documents.upload', $adherent) }}" enctype="multipart/form-data">
                @csrf
                <div class="modal-body">
                    <div class="form-group">
                        <label>Type de document</label>
                        <select name="type" class="form-control" required>
                            <option value="">Sélectionner</option>
                            <option value="cni">Carte Nationale d'Identité</option>
                            <option value="passeport">Passeport</option>
                            <option value="acte_naissance">Acte de naissance</option>
                            <option value="certificat_travail">Certificat de travail</option>
                            <option value="bulletin_salaire">Bulletin de salaire</option>
                            <option value="photo">Photo</option>
                            <option value="autre">Autre</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Fichier</label>
                        <input type="file" name="document" class="form-control" 
                               accept=".pdf,.jpg,.jpeg,.png" required>
                        <small class="text-muted">Formats acceptés: PDF, JPG, PNG (max 5MB)</small>
                    </div>
                    <div class="form-group">
                        <label>Description (optionnel)</label>
                        <textarea name="description" class="form-control" rows="2"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Télécharger</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal de suspension -->
<div class="modal fade" id="suspendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Suspendre l'adhérent</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form method="POST" action="{{ route('adherents.suspend', $adherent) }}">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="form-group">
                        <label>Motif de suspension (optionnel)</label>
                        <textarea name="reason" class="form-control" rows="3" 
                                  placeholder="Précisez le motif de la suspension..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-warning">Suspendre</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
console.log('Script adherent show chargé');

// Fonction pour suspendre l'adhérent (compatible Bootstrap 4)
function suspendAdherent() {
    console.log('suspendAdherent appelé');

    // Vérifier si jQuery est disponible
    if (typeof $ === 'undefined') {
        console.error('jQuery non disponible');
        alert('Erreur: jQuery non chargé');
        return;
    }

    // Bootstrap 4 avec jQuery
    $('#suspendModal').modal('show');
}

// Fonction pour envoyer une notification à cet adhérent spécifique
function sendNotificationToAdherent(adherentId) {
    console.log('sendNotificationToAdherent appelé pour:', adherentId);

    const notificationTypes = [
        { value: 'cotisation_rappel', text: 'Rappel de cotisation' },
        { value: 'document_manquant', text: 'Document manquant' },
        { value: 'rdv_convocation', text: 'Convocation rendez-vous' },
        { value: 'info_generale', text: 'Information générale' }
    ];

    let options = notificationTypes.map(type =>
        `<option value="${type.value}">${type.text}</option>`
    ).join('');

    const modalHtml = `
        <div class="modal fade" id="notificationAdherentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Envoyer une notification</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="notificationAdherentForm">
                            <input type="hidden" name="adherent_id" value="${adherentId}">
                            <div class="form-group">
                                <label>Type de notification</label>
                                <select class="form-control" name="type" required>
                                    <option value="">Sélectionner</option>
                                    ${options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Message</label>
                                <textarea class="form-control" name="message" rows="4"
                                          placeholder="Votre message..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label>Mode d'envoi</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="send_email" value="1" checked>
                                    <label class="form-check-label">Email</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="send_sms" value="1">
                                    <label class="form-check-label">SMS</label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-info" onclick="confirmSendNotificationToAdherent()">Envoyer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprimer le modal existant s'il y en a un
    $('#notificationAdherentModal').remove();

    // Ajouter le modal au DOM
    $('body').append(modalHtml);

    // Afficher le modal
    $('#notificationAdherentModal').modal('show');
}

// Fonction pour confirmer l'envoi de notification
function confirmSendNotificationToAdherent() {
    const form = document.getElementById('notificationAdherentForm');
    const formData = new FormData(form);

    if (!formData.get('type') || !formData.get('message')) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }

    // Simulation d'envoi
    $('#notificationAdherentModal').modal('hide');

    // Afficher un message de succès
    setTimeout(() => {
        alert('Notification envoyée avec succès à l\'adhérent !');
    }, 500);

    // TODO: Implémenter l'envoi réel via AJAX
    // fetch('/api/adherents/' + formData.get('adherent_id') + '/notify', { method: 'POST', body: formData })
}

// Fonction pour exporter les données de cet adhérent
function exportAdherentData(adherentId) {
    console.log('exportAdherentData appelé pour:', adherentId);

    const exportTypes = [
        { value: 'profile', text: 'Profil complet', url: `/adherents/${adherentId}/export/profile` },
        { value: 'cotisations', text: 'Historique cotisations', url: `/adherents/${adherentId}/export/cotisations` },
        { value: 'documents', text: 'Documents', url: `/adherents/${adherentId}/export/documents` },
        { value: 'pension', text: 'Informations pension', url: `/adherents/${adherentId}/export/pension` }
    ];

    let options = exportTypes.map(type =>
        `<option value="${type.value}" data-url="${type.url}">${type.text}</option>`
    ).join('');

    const modalHtml = `
        <div class="modal fade" id="exportAdherentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Exporter les données de l'adhérent</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="exportAdherentForm">
                            <div class="form-group">
                                <label>Type de données</label>
                                <select class="form-control" name="type" required>
                                    <option value="">Sélectionner</option>
                                    ${options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Format</label>
                                <select class="form-control" name="format" required>
                                    <option value="">Sélectionner</option>
                                    <option value="pdf">PDF</option>
                                    <option value="excel">Excel (.xlsx)</option>
                                    <option value="csv">CSV</option>
                                </select>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-warning" onclick="confirmExportAdherentData()">Exporter</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Supprimer le modal existant s'il y en a un
    $('#exportAdherentModal').remove();

    // Ajouter le modal au DOM
    $('body').append(modalHtml);

    // Afficher le modal
    $('#exportAdherentModal').modal('show');
}

// Fonction pour confirmer l'export des données
function confirmExportAdherentData() {
    const form = document.getElementById('exportAdherentForm');
    const formData = new FormData(form);

    if (!formData.get('type') || !formData.get('format')) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }

    // Récupérer l'URL d'export
    const typeSelect = form.querySelector('select[name="type"]');
    const selectedOption = typeSelect.options[typeSelect.selectedIndex];
    const baseUrl = selectedOption.getAttribute('data-url');

    if (!baseUrl) {
        alert('URL d\'export non trouvée');
        return;
    }

    // Construire l'URL avec le format
    const exportUrl = `${baseUrl}?format=${formData.get('format')}`;

    // Fermer le modal
    $('#exportAdherentModal').modal('hide');

    // Ouvrir l'export dans un nouvel onglet
    window.open(exportUrl, '_blank');
}
</script>
@endpush
