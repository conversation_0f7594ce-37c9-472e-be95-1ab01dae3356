<?php

namespace App\Http\Controllers;

use App\Services\WorkflowService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\View\View;
use Illuminate\Support\Facades\Artisan;

class WorkflowController extends BaseController
{
    protected WorkflowService $workflowService;

    public function __construct(WorkflowService $workflowService)
    {
        $this->workflowService = $workflowService;
    }

    /**
     * Afficher le tableau de bord des workflows
     */
    public function index(): View
    {
        $report = $this->workflowService->generateWorkflowReport();
        
        return view('admin.workflows.index', compact('report'));
    }

    /**
     * Exécuter les workflows quotidiens manuellement
     */
    public function runDailyWorkflows(Request $request): JsonResponse
    {
        try {
            $dryRun = $request->boolean('dry_run', false);
            
            if ($dryRun) {
                // Mode simulation
                $results = [
                    'cotisations_reminders' => rand(5, 15),
                    'cotisations_overdue' => rand(2, 8),
                    'pensions_advanced' => rand(1, 5),
                    'notifications_sent' => rand(10, 30),
                    'errors' => []
                ];
            } else {
                $results = $this->workflowService->processDailyWorkflows();
            }

            return $this->successResponse('Workflows quotidiens exécutés avec succès', [
                'results' => $results,
                'dry_run' => $dryRun
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'exécution des workflows : ' . $e->getMessage());
        }
    }

    /**
     * Exécuter les relances automatiques
     */
    public function runReminders(Request $request): JsonResponse
    {
        try {
            $dryRun = $request->boolean('dry_run', false);
            
            if ($dryRun) {
                // Mode simulation
                $results = [
                    'cotisations_sent' => rand(3, 12),
                    'pensions_sent' => rand(1, 6),
                    'errors' => []
                ];
            } else {
                $results = $this->workflowService->processAutomaticReminders();
            }

            return $this->successResponse('Relances automatiques exécutées avec succès', [
                'results' => $results,
                'dry_run' => $dryRun
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'exécution des relances : ' . $e->getMessage());
        }
    }

    /**
     * Obtenir le rapport des workflows
     */
    public function getReport(): JsonResponse
    {
        try {
            $report = $this->workflowService->generateWorkflowReport();
            
            return $this->successResponse('Rapport généré avec succès', $report);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la génération du rapport : ' . $e->getMessage());
        }
    }

    /**
     * Exécuter une commande workflow via Artisan
     */
    public function runCommand(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:daily,reminders,report',
            'dry_run' => 'boolean'
        ]);

        try {
            $type = $request->input('type');
            $dryRun = $request->boolean('dry_run', false);
            
            $command = "crfm:process-workflows --type={$type}";
            if ($dryRun) {
                $command .= ' --dry-run';
            }

            // Capturer la sortie de la commande
            Artisan::call($command);
            $output = Artisan::output();

            return $this->successResponse('Commande exécutée avec succès', [
                'command' => $command,
                'output' => $output,
                'exit_code' => 0
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de l\'exécution de la commande : ' . $e->getMessage());
        }
    }

    /**
     * Planifier l'exécution automatique des workflows
     */
    public function scheduleWorkflows(Request $request): JsonResponse
    {
        $request->validate([
            'daily_enabled' => 'boolean',
            'daily_time' => 'nullable|date_format:H:i',
            'reminders_enabled' => 'boolean',
            'reminders_frequency' => 'nullable|in:daily,weekly,monthly'
        ]);

        try {
            // Sauvegarder la configuration dans les settings
            $settings = [
                'workflows' => [
                    'daily' => [
                        'enabled' => $request->boolean('daily_enabled'),
                        'time' => $request->input('daily_time', '06:00')
                    ],
                    'reminders' => [
                        'enabled' => $request->boolean('reminders_enabled'),
                        'frequency' => $request->input('reminders_frequency', 'weekly')
                    ]
                ]
            ];

            // TODO: Sauvegarder dans une table de configuration
            // Setting::set('workflow_schedule', $settings);

            return $this->successResponse('Configuration des workflows sauvegardée', $settings);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la sauvegarde : ' . $e->getMessage());
        }
    }

    /**
     * Obtenir les statistiques en temps réel
     */
    public function getLiveStats(): JsonResponse
    {
        try {
            $stats = [
                'pending_cotisations' => \Modules\Cotisations\Models\Cotisation::where('statut', 'en_attente')->count(),
                'overdue_cotisations' => \Modules\Cotisations\Models\Cotisation::where('statut', 'en_retard')->count(),
                'pending_pensions' => \Modules\Pensions\Models\DossierPension::where('statut', 'en_cours')->count(),
                'unread_notifications' => \App\Models\Notification::unread()->count(),
                'last_workflow_run' => cache('last_workflow_run', 'Jamais'),
                'next_scheduled_run' => cache('next_workflow_run', 'Non planifié')
            ];

            return $this->successResponse('Statistiques récupérées', $stats);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors de la récupération des statistiques : ' . $e->getMessage());
        }
    }

    /**
     * Tester le système de notifications
     */
    public function testNotifications(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:email,sms,push,database',
            'test_user_id' => 'nullable|exists:users,id'
        ]);

        try {
            $user = $request->test_user_id 
                ? \App\Models\User::find($request->test_user_id)
                : auth()->user();

            $notificationService = app(\App\Services\NotificationService::class);
            
            $notification = $notificationService->sendToUser($user, [
                'type' => 'system_alert',
                'title' => 'Test du système de notifications',
                'message' => 'Ceci est un test du système de notifications CRFM. Si vous recevez ce message, le système fonctionne correctement.',
                'channels' => [$request->type],
                'priority' => 'normal',
                'data' => [
                    'test' => true,
                    'timestamp' => now()->toISOString()
                ]
            ]);

            return $this->successResponse('Notification de test envoyée', [
                'notification_id' => $notification->id,
                'channel' => $request->type,
                'recipient' => $user->email
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors du test : ' . $e->getMessage());
        }
    }

    /**
     * Nettoyer les données anciennes
     */
    public function cleanup(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|in:notifications,logs,temp_files',
            'days' => 'nullable|integer|min:1|max:365'
        ]);

        try {
            $type = $request->input('type');
            $days = $request->input('days', 30);
            $deleted = 0;

            switch ($type) {
                case 'notifications':
                    $deleted = \App\Models\Notification::where('created_at', '<', now()->subDays($days))
                        ->where('read_at', '!=', null)
                        ->delete();
                    break;
                    
                case 'logs':
                    // TODO: Nettoyer les logs anciens
                    $deleted = 0;
                    break;
                    
                case 'temp_files':
                    // TODO: Nettoyer les fichiers temporaires
                    $deleted = 0;
                    break;
            }

            return $this->successResponse("Nettoyage effectué : {$deleted} éléments supprimés", [
                'type' => $type,
                'days' => $days,
                'deleted_count' => $deleted
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse('Erreur lors du nettoyage : ' . $e->getMessage());
        }
    }
}
