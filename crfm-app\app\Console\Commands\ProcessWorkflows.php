<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\WorkflowService;
use Illuminate\Support\Facades\Log;

class ProcessWorkflows extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crfm:process-workflows 
                            {--type=daily : Type de workflow à traiter (daily, reminders, report)}
                            {--dry-run : Exécuter en mode test sans modifications}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Traiter les workflows automatiques CRFM (cotisations, pensions, notifications)';

    protected WorkflowService $workflowService;

    /**
     * Create a new command instance.
     */
    public function __construct(WorkflowService $workflowService)
    {
        parent::__construct();
        $this->workflowService = $workflowService;
    }

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $type = $this->option('type');
        $dryRun = $this->option('dry-run');

        if ($dryRun) {
            $this->warn('🧪 Mode test activé - Aucune modification ne sera effectuée');
        }

        $this->info("🚀 Démarrage du traitement des workflows CRFM - Type: {$type}");
        $startTime = microtime(true);

        try {
            switch ($type) {
                case 'daily':
                    $results = $this->processDailyWorkflows($dryRun);
                    break;
                    
                case 'reminders':
                    $results = $this->processReminders($dryRun);
                    break;
                    
                case 'report':
                    $results = $this->generateReport();
                    break;
                    
                default:
                    $this->error("❌ Type de workflow non reconnu: {$type}");
                    return 1;
            }

            $this->displayResults($results, $type);
            
            $duration = round(microtime(true) - $startTime, 2);
            $this->info("✅ Traitement terminé en {$duration}s");
            
            return 0;
            
        } catch (\Exception $e) {
            $this->error("❌ Erreur lors du traitement: " . $e->getMessage());
            Log::error('Erreur commande workflow: ' . $e->getMessage(), [
                'type' => $type,
                'dry_run' => $dryRun,
                'trace' => $e->getTraceAsString()
            ]);
            
            return 1;
        }
    }

    /**
     * Traiter les workflows quotidiens
     */
    protected function processDailyWorkflows(bool $dryRun): array
    {
        $this->info('📅 Traitement des workflows quotidiens...');
        
        if ($dryRun) {
            return $this->simulateDailyWorkflows();
        }
        
        return $this->workflowService->processDailyWorkflows();
    }

    /**
     * Traiter les relances
     */
    protected function processReminders(bool $dryRun): array
    {
        $this->info('📧 Traitement des relances automatiques...');
        
        if ($dryRun) {
            return $this->simulateReminders();
        }
        
        return $this->workflowService->processAutomaticReminders();
    }

    /**
     * Générer un rapport
     */
    protected function generateReport(): array
    {
        $this->info('📊 Génération du rapport de workflow...');
        
        return $this->workflowService->generateWorkflowReport();
    }

    /**
     * Simuler les workflows quotidiens (mode test)
     */
    protected function simulateDailyWorkflows(): array
    {
        // Simulation des données pour le mode test
        return [
            'cotisations_reminders' => rand(5, 15),
            'cotisations_overdue' => rand(2, 8),
            'pensions_advanced' => rand(1, 5),
            'notifications_sent' => rand(10, 30),
            'errors' => []
        ];
    }

    /**
     * Simuler les relances (mode test)
     */
    protected function simulateReminders(): array
    {
        return [
            'cotisations_sent' => rand(3, 12),
            'pensions_sent' => rand(1, 6),
            'errors' => []
        ];
    }

    /**
     * Afficher les résultats
     */
    protected function displayResults(array $results, string $type): void
    {
        $this->newLine();
        $this->info('📋 Résultats du traitement:');
        $this->newLine();

        switch ($type) {
            case 'daily':
                $this->displayDailyResults($results);
                break;
                
            case 'reminders':
                $this->displayReminderResults($results);
                break;
                
            case 'report':
                $this->displayReportResults($results);
                break;
        }

        if (!empty($results['errors'])) {
            $this->newLine();
            $this->error('⚠️  Erreurs rencontrées:');
            foreach ($results['errors'] as $error) {
                $this->line("   • {$error}");
            }
        }
    }

    /**
     * Afficher les résultats quotidiens
     */
    protected function displayDailyResults(array $results): void
    {
        $this->table(
            ['Traitement', 'Nombre'],
            [
                ['Rappels de cotisations envoyés', $results['cotisations_reminders'] ?? 0],
                ['Cotisations marquées en retard', $results['cotisations_overdue'] ?? 0],
                ['Dossiers de pension avancés', $results['pensions_advanced'] ?? 0],
                ['Total notifications envoyées', $results['notifications_sent'] ?? 0],
            ]
        );
    }

    /**
     * Afficher les résultats des relances
     */
    protected function displayReminderResults(array $results): void
    {
        $this->table(
            ['Type de relance', 'Nombre envoyé'],
            [
                ['Relances cotisations', $results['cotisations_sent'] ?? 0],
                ['Relances pensions', $results['pensions_sent'] ?? 0],
            ]
        );
    }

    /**
     * Afficher le rapport
     */
    protected function displayReportResults(array $results): void
    {
        $this->info('💰 Cotisations:');
        $this->table(
            ['Statut', 'Nombre'],
            [
                ['En attente', $results['cotisations']['en_attente'] ?? 0],
                ['En retard', $results['cotisations']['en_retard'] ?? 0],
                ['Payées', $results['cotisations']['payees'] ?? 0],
            ]
        );

        $this->newLine();
        $this->info('🏛️  Pensions:');
        $this->table(
            ['Statut', 'Nombre'],
            [
                ['En cours', $results['pensions']['en_cours'] ?? 0],
                ['Liquidées', $results['pensions']['liquides'] ?? 0],
                ['Rejetées', $results['pensions']['rejetes'] ?? 0],
            ]
        );

        $this->newLine();
        $this->info('🔔 Notifications:');
        $this->table(
            ['Type', 'Nombre'],
            [
                ['Total', $results['notifications']['total'] ?? 0],
                ['Non lues', $results['notifications']['unread'] ?? 0],
                ['Envoyées aujourd\'hui', $results['notifications']['sent_today'] ?? 0],
            ]
        );
    }
}
