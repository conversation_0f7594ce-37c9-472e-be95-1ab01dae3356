<?php

use Illuminate\Support\Facades\Route;
use Modules\Reports\Controllers\ReportController;

/*
|--------------------------------------------------------------------------
| Reports Module Routes
|--------------------------------------------------------------------------
*/

Route::middleware(['web', 'auth'])->group(function () {

    // Routes requiring view permission
    Route::middleware('permission:view-reports')->group(function () {
        Route::get('/reports', [ReportController::class, 'index'])->name('reports.index');
        Route::get('/api/reports/dashboard-stats', [ReportController::class, 'getDashboardStats'])->name('reports.api.dashboard-stats');

        // Report forms
        Route::get('/reports/adherents/form', [ReportController::class, 'adherentsForm'])->name('reports.adherents.form');
        Route::get('/reports/cotisations/form', [ReportController::class, 'cotisationsForm'])->name('reports.cotisations.form');
        Route::get('/reports/pensions/form', [ReportController::class, 'pensionsForm'])->name('reports.pensions.form');
        Route::get('/reports/financial/form', [ReportController::class, 'financialForm'])->name('reports.financial.form');

        // Report generation
        Route::post('/reports/adherents', [ReportController::class, 'adherentsReport'])->name('reports.adherents.generate');
        Route::post('/reports/cotisations', [ReportController::class, 'cotisationsReport'])->name('reports.cotisations.generate');
        Route::post('/reports/pensions', [ReportController::class, 'pensionsReport'])->name('reports.pensions.generate');
        Route::post('/reports/financial', [ReportController::class, 'financialReport'])->name('reports.financial.generate');

        // API endpoints
        Route::post('/api/reports/export', [ReportController::class, 'exportData'])->name('reports.api.export');
        Route::post('/api/reports/preview', [ReportController::class, 'preview'])->name('reports.api.preview');
    });

    // Routes requiring generate permission
    Route::middleware('permission:generate-reports')->group(function () {
        Route::get('/reports/builder', [ReportController::class, 'builder'])->name('reports.builder');
        Route::post('/reports/custom', [ReportController::class, 'customReport'])->name('reports.custom');
    });

});