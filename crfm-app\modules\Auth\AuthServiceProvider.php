<?php

namespace Modules\Auth;

use App\Providers\BaseModuleServiceProvider;
use Modules\Auth\Models\Role;
use Modules\Auth\Models\Permission;
use Modules\Auth\Repositories\UserRepository;
use Modules\Auth\Repositories\RoleRepository;
use Modules\Auth\Repositories\PermissionRepository;
use Modules\Auth\Services\AuthService;
use Modules\Auth\Services\RoleService;
use Modules\Auth\Services\PermissionService;

class AuthServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "Auth";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register repositories
        $this->app->bind(UserRepository::class, function ($app) {
            return new UserRepository(new \App\Models\User());
        });

        $this->app->bind(RoleRepository::class, function ($app) {
            return new RoleRepository(new Role());
        });

        $this->app->bind(PermissionRepository::class, function ($app) {
            return new PermissionRepository(new Permission());
        });

        // Register services
        $this->app->bind(AuthService::class);
        $this->app->bind(RoleService::class);
        $this->app->bind(PermissionService::class);
    }

    /**
     * Boot module
     */
    public function boot(): void
    {
        parent::boot();

        // Register gates and policies
        $this->registerGates();
    }

    /**
     * Register authorization gates
     */
    protected function registerGates(): void
    {
        // Gates will be registered here
    }
}
