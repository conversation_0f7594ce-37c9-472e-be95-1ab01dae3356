<?php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

try {
    $service = app(\Modules\Dashboard\Services\DashboardService::class);
    $stats = $service->getQuickStats();
    
    echo "=== STATISTIQUES DASHBOARD ===\n";
    echo "Total adhérents: " . $stats['total_adherents'] . "\n";
    echo "Adhérents actifs: " . $stats['adherents_actifs'] . "\n";
    echo "Adhérents suspendus: " . $stats['adherents_suspendus'] . "\n";
    echo "Cotisations ce mois: " . number_format($stats['cotisations_mois']) . " €\n";
    echo "Cotisations payées: " . $stats['cotisations_payees'] . "\n";
    echo "Cotisations en retard: " . $stats['cotisations_en_retard'] . "\n";
    echo "Total cotisations payées: " . number_format($stats['montant_total_cotisations']) . " €\n";
    echo "Pensions en cours: " . $stats['pensions_en_cours'] . "\n";
    echo "Pensions liquidées: " . $stats['pensions_liquidees'] . "\n";
    echo "Utilisateurs actifs: " . $stats['utilisateurs_actifs'] . "\n";
    
} catch (Exception $e) {
    echo "Erreur: " . $e->getMessage() . "\n";
    echo "Trace: " . $e->getTraceAsString() . "\n";
}
