@extends('layouts.app')

@section('title', 'Gestion des Workflows')

@section('content')
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="page-title-box">
                <h4 class="page-title">Gestion des Workflows Automatiques</h4>
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">Tableau de bord</a></li>
                    <li class="breadcrumb-item active">Workflows</li>
                </ol>
            </div>
        </div>
    </div>

    <!-- Statistiques en temps réel -->
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-primary text-primary rounded">
                                    <i class="feather icon-clock"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1" id="pending-cotisations">{{ $report['cotisations']['en_attente'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Cotisations en attente</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-warning text-warning rounded">
                                    <i class="feather icon-alert-triangle"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1" id="overdue-cotisations">{{ $report['cotisations']['en_retard'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Cotisations en retard</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-info text-info rounded">
                                    <i class="feather icon-trending-up"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1" id="pending-pensions">{{ $report['pensions']['en_cours'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Dossiers pension en cours</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0 me-3">
                            <div class="avatar-sm">
                                <div class="avatar-title bg-soft-danger text-danger rounded">
                                    <i class="feather icon-bell"></i>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1">
                            <h5 class="mb-1" id="unread-notifications">{{ $report['notifications']['unread'] ?? 0 }}</h5>
                            <p class="text-muted mb-0">Notifications non lues</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions rapides -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions Rapides</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button type="button" class="btn btn-primary btn-block" onclick="runWorkflow('daily')">
                                <i class="feather icon-play-circle me-2"></i>
                                Exécuter Workflows Quotidiens
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button type="button" class="btn btn-warning btn-block" onclick="runWorkflow('reminders')">
                                <i class="feather icon-mail me-2"></i>
                                Envoyer Relances
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button type="button" class="btn btn-info btn-block" onclick="testNotifications()">
                                <i class="feather icon-send me-2"></i>
                                Tester Notifications
                            </button>
                        </div>
                        <div class="col-md-6 col-lg-3 mb-3">
                            <button type="button" class="btn btn-secondary btn-block" onclick="refreshStats()">
                                <i class="feather icon-refresh-cw me-2"></i>
                                Actualiser Stats
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Rapport détaillé -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">État des Cotisations</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td>En attente</td>
                                    <td class="text-end">
                                        <span class="badge bg-primary">{{ $report['cotisations']['en_attente'] ?? 0 }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>En retard</td>
                                    <td class="text-end">
                                        <span class="badge bg-warning">{{ $report['cotisations']['en_retard'] ?? 0 }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Payées</td>
                                    <td class="text-end">
                                        <span class="badge bg-success">{{ $report['cotisations']['payees'] ?? 0 }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">État des Pensions</h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tbody>
                                <tr>
                                    <td>En cours</td>
                                    <td class="text-end">
                                        <span class="badge bg-info">{{ $report['pensions']['en_cours'] ?? 0 }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Liquidées</td>
                                    <td class="text-end">
                                        <span class="badge bg-success">{{ $report['pensions']['liquides'] ?? 0 }}</span>
                                    </td>
                                </tr>
                                <tr>
                                    <td>Rejetées</td>
                                    <td class="text-end">
                                        <span class="badge bg-danger">{{ $report['pensions']['rejetes'] ?? 0 }}</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Historique des exécutions -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Historique des Exécutions</h5>
                </div>
                <div class="card-body">
                    <div id="execution-history">
                        <p class="text-muted">Aucune exécution récente</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modal de configuration -->
<div class="modal fade" id="configModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Configuration des Workflows</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <form id="configForm">
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="dailyEnabled" name="daily_enabled">
                            <label class="form-check-label" for="dailyEnabled">
                                Activer les workflows quotidiens
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="dailyTime">Heure d'exécution quotidienne</label>
                        <input type="time" class="form-control" id="dailyTime" name="daily_time" value="06:00">
                    </div>
                    <div class="form-group">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="remindersEnabled" name="reminders_enabled">
                            <label class="form-check-label" for="remindersEnabled">
                                Activer les relances automatiques
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="remindersFrequency">Fréquence des relances</label>
                        <select class="form-control" id="remindersFrequency" name="reminders_frequency">
                            <option value="daily">Quotidienne</option>
                            <option value="weekly" selected>Hebdomadaire</option>
                            <option value="monthly">Mensuelle</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                <button type="button" class="btn btn-primary" onclick="saveConfiguration()">Sauvegarder</button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
console.log('Workflows admin page loaded');

// Actualiser les statistiques automatiquement
setInterval(refreshStats, 60000); // Toutes les minutes

/**
 * Exécuter un workflow
 */
function runWorkflow(type) {
    const dryRun = confirm('Voulez-vous exécuter en mode test (sans modifications réelles) ?');
    
    if (window.CRFMNotifications) {
        window.CRFMNotifications.showToast(`Exécution du workflow ${type}...`, 'info');
    }
    
    $.ajax({
        url: `/api/workflows/${type}`,
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({ dry_run: dryRun }),
        success: function(response) {
            if (response.success) {
                displayWorkflowResults(response.data.results, type, dryRun);
                refreshStats();
            } else {
                if (window.CRFMNotifications) {
                    window.CRFMNotifications.showToast('Erreur lors de l\'exécution', 'error');
                }
            }
        },
        error: function(xhr) {
            console.error('Erreur workflow:', xhr);
            if (window.CRFMNotifications) {
                window.CRFMNotifications.showToast('Erreur lors de l\'exécution', 'error');
            }
        }
    });
}

/**
 * Afficher les résultats d'un workflow
 */
function displayWorkflowResults(results, type, dryRun) {
    let message = `Workflow ${type} ${dryRun ? '(test)' : ''} terminé:\n`;
    
    if (type === 'daily') {
        message += `• ${results.cotisations_reminders || 0} rappels de cotisations\n`;
        message += `• ${results.cotisations_overdue || 0} cotisations en retard\n`;
        message += `• ${results.pensions_advanced || 0} dossiers pension avancés`;
    } else if (type === 'reminders') {
        message += `• ${results.cotisations_sent || 0} relances cotisations\n`;
        message += `• ${results.pensions_sent || 0} relances pensions`;
    }
    
    if (window.CRFMNotifications) {
        window.CRFMNotifications.showToast(message, 'success', 8000);
    } else {
        alert(message);
    }
}

/**
 * Actualiser les statistiques
 */
function refreshStats() {
    $.ajax({
        url: '/api/workflows/stats',
        method: 'GET',
        success: function(response) {
            if (response.success) {
                const data = response.data;
                $('#pending-cotisations').text(data.pending_cotisations || 0);
                $('#overdue-cotisations').text(data.overdue_cotisations || 0);
                $('#pending-pensions').text(data.pending_pensions || 0);
                $('#unread-notifications').text(data.unread_notifications || 0);
            }
        },
        error: function(xhr) {
            console.error('Erreur actualisation stats:', xhr);
        }
    });
}

/**
 * Tester les notifications
 */
function testNotifications() {
    const channel = prompt('Canal de test (email, sms, push, database):', 'email');
    if (!channel) return;
    
    $.ajax({
        url: '/api/workflows/test-notifications',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
            'Content-Type': 'application/json'
        },
        data: JSON.stringify({ type: channel }),
        success: function(response) {
            if (response.success) {
                if (window.CRFMNotifications) {
                    window.CRFMNotifications.showToast('Notification de test envoyée !', 'success');
                } else {
                    alert('Notification de test envoyée !');
                }
            }
        },
        error: function(xhr) {
            console.error('Erreur test notification:', xhr);
            if (window.CRFMNotifications) {
                window.CRFMNotifications.showToast('Erreur lors du test', 'error');
            }
        }
    });
}

/**
 * Sauvegarder la configuration
 */
function saveConfiguration() {
    const formData = new FormData(document.getElementById('configForm'));
    
    $.ajax({
        url: '/api/workflows/schedule',
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
        },
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            if (response.success) {
                $('#configModal').modal('hide');
                if (window.CRFMNotifications) {
                    window.CRFMNotifications.showToast('Configuration sauvegardée', 'success');
                }
            }
        },
        error: function(xhr) {
            console.error('Erreur sauvegarde config:', xhr);
            if (window.CRFMNotifications) {
                window.CRFMNotifications.showToast('Erreur lors de la sauvegarde', 'error');
            }
        }
    });
}
</script>
@endpush
