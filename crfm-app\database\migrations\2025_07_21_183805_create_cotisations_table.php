<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('cotisations', function (Blueprint $table) {
            $table->id();
            $table->uuid('uuid')->unique();
            $table->foreignId('adherent_id')->constrained()->onDelete('cascade');

            // Identification
            $table->string('numero_cotisation')->unique();
            $table->string('periode_cotisation'); // ex: "2024-01", "2024-Q1"
            $table->integer('annee_cotisation');
            $table->integer('mois_cotisation')->nullable();

            // Montants
            $table->decimal('montant_base', 15, 2); // Salaire de base
            $table->decimal('taux_cotisation', 5, 4); // ex: 12.5000 pour 12.5%
            $table->decimal('montant_cotisation', 15, 2); // Montant total
            $table->decimal('montant_employeur', 15, 2); // Part employeur
            $table->decimal('montant_employe', 15, 2); // Part employé

            // Dates
            $table->date('date_echeance');
            $table->timestamp('date_paiement')->nullable();

            // Paiement
            $table->enum('mode_paiement', ['especes', 'cheque', 'virement', 'mobile_money', 'prelevement'])->nullable();
            $table->string('reference_paiement')->nullable();

            // Statut et suivi
            $table->enum('statut', ['en_attente', 'payee', 'en_retard', 'annulee'])->default('en_attente');
            $table->text('observations')->nullable();

            // Audit
            $table->foreignId('created_by')->constrained('users')->onDelete('cascade');
            $table->foreignId('validated_by')->nullable()->constrained('users')->onDelete('set null');
            $table->timestamp('validated_at')->nullable();

            $table->timestamps();
            $table->softDeletes();

            // Index
            $table->index(['adherent_id', 'statut']);
            $table->index(['periode_cotisation']);
            $table->index(['annee_cotisation', 'mois_cotisation']);
            $table->index(['date_echeance']);
            $table->index(['statut']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('cotisations');
    }
};
