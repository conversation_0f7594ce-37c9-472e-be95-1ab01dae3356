<?php

namespace Modules\Adherents;

use App\Providers\BaseModuleServiceProvider;

class AdherentsServiceProvider extends BaseModuleServiceProvider
{
    /**
     * Get module name
     */
    protected function getModuleName(): string
    {
        return "Adherents";
    }

    /**
     * Register module services
     */
    protected function registerServices(): void
    {
        // Register repositories
        $this->app->bind(\Modules\Adherents\Repositories\AdherentRepository::class, function ($app) {
            return new \Modules\Adherents\Repositories\AdherentRepository(new \Modules\Adherents\Models\Adherent());
        });

        // Register services
        $this->app->bind(\Modules\Adherents\Services\AdherentService::class);
    }
}