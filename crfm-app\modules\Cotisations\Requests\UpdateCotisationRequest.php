<?php

namespace Modules\Cotisations\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdateCotisationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->user()->can('manage-cotisations');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $cotisationId = $this->route('cotisation')->id;

        return [
            'adherent_id' => 'required|exists:adherents,id',
            'numero_cotisation' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('cotisations', 'numero_cotisation')->ignore($cotisationId)
            ],
            'periode_cotisation' => 'required|string|max:20',
            'annee_cotisation' => 'required|integer|min:2020|max:' . (date('Y') + 1),
            'mois_cotisation' => 'nullable|integer|min:1|max:12',
            'montant_base' => 'required|numeric|min:0',
            'taux_cotisation' => 'required|numeric|min:0|max:100',
            'date_echeance' => 'required|date',
            'date_paiement' => 'nullable|date',
            'mode_paiement' => 'nullable|in:especes,cheque,virement,mobile_money,prelevement',
            'reference_paiement' => 'nullable|string|max:255',
            'statut' => 'required|in:en_attente,payee,en_retard,annulee',
            'observations' => 'nullable|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'adherent_id.required' => 'L\'adhérent est obligatoire.',
            'adherent_id.exists' => 'L\'adhérent sélectionné n\'existe pas.',
            'numero_cotisation.unique' => 'Ce numéro de cotisation existe déjà.',
            'periode_cotisation.required' => 'La période de cotisation est obligatoire.',
            'annee_cotisation.required' => 'L\'année de cotisation est obligatoire.',
            'annee_cotisation.min' => 'L\'année de cotisation ne peut pas être antérieure à 2020.',
            'annee_cotisation.max' => 'L\'année de cotisation ne peut pas être supérieure à l\'année prochaine.',
            'mois_cotisation.min' => 'Le mois doit être entre 1 et 12.',
            'mois_cotisation.max' => 'Le mois doit être entre 1 et 12.',
            'montant_base.required' => 'Le montant de base est obligatoire.',
            'montant_base.numeric' => 'Le montant de base doit être un nombre.',
            'montant_base.min' => 'Le montant de base ne peut pas être négatif.',
            'taux_cotisation.required' => 'Le taux de cotisation est obligatoire.',
            'taux_cotisation.numeric' => 'Le taux de cotisation doit être un nombre.',
            'taux_cotisation.min' => 'Le taux de cotisation ne peut pas être négatif.',
            'taux_cotisation.max' => 'Le taux de cotisation ne peut pas dépasser 100%.',
            'date_echeance.required' => 'La date d\'échéance est obligatoire.',
            'date_echeance.date' => 'La date d\'échéance doit être une date valide.',
            'date_paiement.date' => 'La date de paiement doit être une date valide.',
            'mode_paiement.in' => 'Le mode de paiement sélectionné n\'est pas valide.',
            'statut.required' => 'Le statut est obligatoire.',
            'statut.in' => 'Le statut sélectionné n\'est pas valide.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Vérifier que la période correspond à l'année et au mois
            if ($this->has('periode_cotisation') && $this->has('annee_cotisation')) {
                $periode = $this->periode_cotisation;
                $annee = $this->annee_cotisation;
                
                if (!str_starts_with($periode, (string)$annee)) {
                    $validator->errors()->add('periode_cotisation', 'La période doit correspondre à l\'année de cotisation.');
                }
            }

            // Vérifier l'unicité de la cotisation pour l'adhérent et la période (sauf pour la cotisation actuelle)
            if ($this->has('adherent_id') && $this->has('periode_cotisation')) {
                $cotisationId = $this->route('cotisation')->id;
                $exists = \Modules\Cotisations\Models\Cotisation::where('adherent_id', $this->adherent_id)
                    ->where('periode_cotisation', $this->periode_cotisation)
                    ->where('id', '!=', $cotisationId)
                    ->exists();
                
                if ($exists) {
                    $validator->errors()->add('periode_cotisation', 'Une cotisation existe déjà pour cet adhérent et cette période.');
                }
            }

            // Vérifier la cohérence des données de paiement
            if ($this->statut === 'payee') {
                if (!$this->has('date_paiement') || empty($this->date_paiement)) {
                    $validator->errors()->add('date_paiement', 'La date de paiement est obligatoire pour une cotisation payée.');
                }
                
                if (!$this->has('mode_paiement') || empty($this->mode_paiement)) {
                    $validator->errors()->add('mode_paiement', 'Le mode de paiement est obligatoire pour une cotisation payée.');
                }
            }
        });
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        // Auto-generate periode if not provided
        if (!$this->has('periode_cotisation') && $this->has('annee_cotisation') && $this->has('mois_cotisation')) {
            $this->merge([
                'periode_cotisation' => $this->annee_cotisation . '-' . str_pad($this->mois_cotisation, 2, '0', STR_PAD_LEFT)
            ]);
        }
    }
}
