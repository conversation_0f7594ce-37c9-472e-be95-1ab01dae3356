/**
 * CRFM Notification System
 * Système de notifications en temps réel pour l'application CRFM
 */

class CRFMNotifications {
    constructor() {
        this.unreadCount = 0;
        this.notifications = [];
        this.refreshInterval = null;
        this.init();
    }

    /**
     * Initialiser le système de notifications
     */
    init() {
        this.setupEventListeners();
        this.loadUnreadNotifications();
        this.startAutoRefresh();
        this.setupToastContainer();
    }

    /**
     * Configurer les écouteurs d'événements
     */
    setupEventListeners() {
        // Clic sur l'icône de notifications
        $(document).on('click', '.notification-toggle', (e) => {
            e.preventDefault();
            this.toggleNotificationDropdown();
        });

        // Marquer une notification comme lue
        $(document).on('click', '.notification-item', (e) => {
            const notificationId = $(e.currentTarget).data('id');
            if (notificationId) {
                this.markAsRead(notificationId);
            }
        });

        // Marquer toutes comme lues
        $(document).on('click', '.mark-all-read', (e) => {
            e.preventDefault();
            this.markAllAsRead();
        });

        // Supprimer une notification
        $(document).on('click', '.delete-notification', (e) => {
            e.preventDefault();
            e.stopPropagation();
            const notificationId = $(e.currentTarget).data('id');
            this.deleteNotification(notificationId);
        });
    }

    /**
     * Charger les notifications non lues
     */
    loadUnreadNotifications() {
        $.ajax({
            url: '/api/notifications/unread',
            method: 'GET',
            success: (response) => {
                if (response.success) {
                    this.notifications = response.data.notifications;
                    this.unreadCount = response.data.count;
                    this.updateNotificationUI();
                }
            },
            error: (xhr) => {
                console.error('Erreur lors du chargement des notifications:', xhr);
            }
        });
    }

    /**
     * Mettre à jour l'interface utilisateur
     */
    updateNotificationUI() {
        // Mettre à jour le badge de compteur
        const badge = $('.notification-badge');
        if (this.unreadCount > 0) {
            badge.text(this.unreadCount > 99 ? '99+' : this.unreadCount).show();
        } else {
            badge.hide();
        }

        // Mettre à jour la liste déroulante
        this.updateNotificationDropdown();
    }

    /**
     * Mettre à jour la liste déroulante des notifications
     */
    updateNotificationDropdown() {
        const dropdown = $('.notification-dropdown-menu');
        
        if (this.notifications.length === 0) {
            dropdown.html(`
                <div class="notification-empty">
                    <i class="feather icon-bell-off"></i>
                    <p>Aucune nouvelle notification</p>
                </div>
            `);
            return;
        }

        let html = '<div class="notification-header">';
        html += `<span>Notifications (${this.unreadCount})</span>`;
        html += '<a href="#" class="mark-all-read">Tout marquer comme lu</a>';
        html += '</div>';

        html += '<div class="notification-list">';
        this.notifications.forEach(notification => {
            html += this.renderNotificationItem(notification);
        });
        html += '</div>';

        html += '<div class="notification-footer">';
        html += '<a href="/notifications" class="btn btn-sm btn-primary">Voir toutes</a>';
        html += '</div>';

        dropdown.html(html);
    }

    /**
     * Rendre un élément de notification
     */
    renderNotificationItem(notification) {
        return `
            <div class="notification-item" data-id="${notification.id}">
                <div class="notification-icon">
                    <i class="${notification.type_icon} text-${notification.priority_color}"></i>
                </div>
                <div class="notification-content">
                    <h6>${notification.title}</h6>
                    <p>${notification.message}</p>
                    <small class="text-muted">${notification.time_ago}</small>
                </div>
                <div class="notification-actions">
                    <button class="btn btn-sm btn-outline-danger delete-notification" data-id="${notification.id}">
                        <i class="feather icon-x"></i>
                    </button>
                </div>
            </div>
        `;
    }

    /**
     * Basculer l'affichage de la liste déroulante
     */
    toggleNotificationDropdown() {
        $('.notification-dropdown').toggleClass('show');
    }

    /**
     * Marquer une notification comme lue
     */
    markAsRead(notificationId) {
        $.ajax({
            url: `/api/notifications/${notificationId}/read`,
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    // Retirer de la liste des non lues
                    this.notifications = this.notifications.filter(n => n.id !== notificationId);
                    this.unreadCount = Math.max(0, this.unreadCount - 1);
                    this.updateNotificationUI();
                }
            },
            error: (xhr) => {
                console.error('Erreur lors du marquage comme lu:', xhr);
            }
        });
    }

    /**
     * Marquer toutes les notifications comme lues
     */
    markAllAsRead() {
        $.ajax({
            url: '/api/notifications/mark-all-read',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    this.notifications = [];
                    this.unreadCount = 0;
                    this.updateNotificationUI();
                    this.showToast('Toutes les notifications ont été marquées comme lues', 'success');
                }
            },
            error: (xhr) => {
                console.error('Erreur lors du marquage global:', xhr);
                this.showToast('Erreur lors du marquage des notifications', 'error');
            }
        });
    }

    /**
     * Supprimer une notification
     */
    deleteNotification(notificationId) {
        $.ajax({
            url: `/api/notifications/${notificationId}`,
            method: 'DELETE',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            success: (response) => {
                if (response.success) {
                    this.notifications = this.notifications.filter(n => n.id !== notificationId);
                    this.unreadCount = Math.max(0, this.unreadCount - 1);
                    this.updateNotificationUI();
                    this.showToast('Notification supprimée', 'success');
                }
            },
            error: (xhr) => {
                console.error('Erreur lors de la suppression:', xhr);
                this.showToast('Erreur lors de la suppression', 'error');
            }
        });
    }

    /**
     * Démarrer le rafraîchissement automatique
     */
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            this.loadUnreadNotifications();
        }, 30000); // Rafraîchir toutes les 30 secondes
    }

    /**
     * Arrêter le rafraîchissement automatique
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    /**
     * Configurer le conteneur de toasts
     */
    setupToastContainer() {
        if (!$('.toast-container').length) {
            $('body').append(`
                <div class="toast-container position-fixed top-0 end-0 p-3" style="z-index: 9999;">
                </div>
            `);
        }
    }

    /**
     * Afficher un toast de notification
     */
    showToast(message, type = 'info', duration = 5000) {
        const toastId = 'toast-' + Date.now();
        const iconClass = {
            'success': 'feather icon-check-circle',
            'error': 'feather icon-alert-circle',
            'warning': 'feather icon-alert-triangle',
            'info': 'feather icon-info'
        }[type] || 'feather icon-info';

        const bgClass = {
            'success': 'bg-success',
            'error': 'bg-danger',
            'warning': 'bg-warning',
            'info': 'bg-info'
        }[type] || 'bg-info';

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white ${bgClass} border-0" role="alert">
                <div class="d-flex">
                    <div class="toast-body">
                        <i class="${iconClass} me-2"></i>
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            </div>
        `;

        $('.toast-container').append(toastHtml);
        
        // Utiliser Bootstrap 4 compatible
        $(`#${toastId}`).fadeIn();
        
        setTimeout(() => {
            $(`#${toastId}`).fadeOut(() => {
                $(`#${toastId}`).remove();
            });
        }, duration);
    }

    /**
     * Envoyer une notification à un adhérent
     */
    sendToAdherent(adherentId, type, message, channels = {}) {
        return $.ajax({
            url: '/api/notifications/send-to-adherent',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
                'Content-Type': 'application/json'
            },
            data: JSON.stringify({
                adherent_id: adherentId,
                type: type,
                message: message,
                send_email: channels.email || false,
                send_sms: channels.sms || false
            })
        });
    }
}

// Initialiser le système de notifications quand le DOM est prêt
$(document).ready(function() {
    window.CRFMNotifications = new CRFMNotifications();
});

// Fonctions globales pour compatibilité
function sendNotificationToAdherent(adherentId) {
    if (!window.CRFMNotifications) {
        console.error('Système de notifications non initialisé');
        return;
    }

    const notificationTypes = [
        { value: 'cotisation_rappel', text: 'Rappel de cotisation' },
        { value: 'document_manquant', text: 'Document manquant' },
        { value: 'rdv_convocation', text: 'Convocation rendez-vous' },
        { value: 'info_generale', text: 'Information générale' }
    ];

    let options = notificationTypes.map(type =>
        `<option value="${type.value}">${type.text}</option>`
    ).join('');

    const modalHtml = `
        <div class="modal fade" id="notificationAdherentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Envoyer une notification</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">
                        <form id="notificationAdherentForm">
                            <input type="hidden" name="adherent_id" value="${adherentId}">
                            <div class="form-group">
                                <label>Type de notification</label>
                                <select class="form-control" name="type" required>
                                    <option value="">Sélectionner</option>
                                    ${options}
                                </select>
                            </div>
                            <div class="form-group">
                                <label>Message</label>
                                <textarea class="form-control" name="message" rows="4"
                                          placeholder="Votre message..." required></textarea>
                            </div>
                            <div class="form-group">
                                <label>Mode d'envoi</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="send_email" value="1" checked>
                                    <label class="form-check-label">Email</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" name="send_sms" value="1">
                                    <label class="form-check-label">SMS</label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-info" onclick="confirmSendNotificationToAdherent()">Envoyer</button>
                    </div>
                </div>
            </div>
        </div>
    `;

    $('#notificationAdherentModal').remove();
    $('body').append(modalHtml);
    $('#notificationAdherentModal').modal('show');
}

function confirmSendNotificationToAdherent() {
    const form = document.getElementById('notificationAdherentForm');
    const formData = new FormData(form);

    if (!formData.get('type') || !formData.get('message')) {
        alert('Veuillez remplir tous les champs obligatoires');
        return;
    }

    const adherentId = formData.get('adherent_id');
    const type = formData.get('type');
    const message = formData.get('message');
    const channels = {
        email: formData.get('send_email') === '1',
        sms: formData.get('send_sms') === '1'
    };

    window.CRFMNotifications.sendToAdherent(adherentId, type, message, channels)
        .then(response => {
            $('#notificationAdherentModal').modal('hide');
            if (response.success) {
                window.CRFMNotifications.showToast('Notification envoyée avec succès !', 'success');
            } else {
                window.CRFMNotifications.showToast('Erreur lors de l\'envoi', 'error');
            }
        })
        .catch(error => {
            console.error('Erreur:', error);
            window.CRFMNotifications.showToast('Erreur lors de l\'envoi', 'error');
        });
}
