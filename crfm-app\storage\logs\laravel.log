[2025-07-30 14:13:15] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' (Connection: mysql, SQL: insert into `cotisations` (`numero_cotisation`, `adherent_id`, `periode_cotisation`, `annee_cotisation`, `mois_cotisation`, `montant_base`, `taux_cotisation`, `montant_cotisation`, `montant_employeur`, `montant_employe`, `date_echeance`, `statut`, `date_paiement`, `mode_paiement`, `reference_paiement`, `observations`, `created_by`, `uuid`, `updated_at`, `created_at`) values (COT202500103, 1, mars, 2025, 3, 1800.00, 0.1083, 195, 117, 78, 2025-03-31 23:59:59, payee, 2025-03-12 14:13:14, virement, REF923892, ?, 1, f7f0908f-ded3-4763-974e-798cebe8b75b, 2025-07-30 14:13:14, 2025-07-30 14:13:14)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' (Connection: mysql, SQL: insert into `cotisations` (`numero_cotisation`, `adherent_id`, `periode_cotisation`, `annee_cotisation`, `mois_cotisation`, `montant_base`, `taux_cotisation`, `montant_cotisation`, `montant_employeur`, `montant_employe`, `date_echeance`, `statut`, `date_paiement`, `mode_paiement`, `reference_paiement`, `observations`, `created_by`, `uuid`, `updated_at`, `created_at`) values (COT202500103, 1, mars, 2025, 3, 1800.00, 0.1083, 195, 117, 78, 2025-03-31 23:59:59, payee, 2025-03-12 14:13:14, virement, REF923892, ?, 1, f7f0908f-ded3-4763-974e-798cebe8b75b, 2025-07-30 14:13:14, 2025-07-30 14:13:14)) at C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `co...', Array, 'id')
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Modules\\Cotisations\\Models\\Cotisation))
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(Modules\\Cotisations\\Models\\Cotisation), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\CotisationSeeder.php(56): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\CotisationSeeder->run()
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#21 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#23 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#24 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#30 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#32 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#39 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#40 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(99): Illuminate\\Console\\Command->call('db:seed', Array)
#45 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#46 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#47 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#48 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#49 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#52 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#53 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#54 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#55 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#56 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' at C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `co...', Array)
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `co...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Modules\\Cotisations\\Models\\Cotisation))
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(Modules\\Cotisations\\Models\\Cotisation), Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\CotisationSeeder.php(56): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\CotisationSeeder->run()
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#23 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#25 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#26 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#32 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#34 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#45 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#46 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(99): Illuminate\\Console\\Command->call('db:seed', Array)
#47 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Migrations\\Migrator.php(641): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#48 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\MigrateCommand.php(83): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#50 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#51 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#53 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#54 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#55 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#56 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#57 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#61 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#62 {main}
"} 
[2025-07-30 14:14:31] local.INFO: User logged in {"user_id":1,"email":"<EMAIL>","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-30 14:42:59] local.ERROR: Erreur commande workflow: SQLSTATE[42S02]: Base table or view not found: 1146 Table 'crfm.notifications' doesn't exist (Connection: mysql, SQL: select count(*) as aggregate from `notifications`) {"type":"report","dry_run":true,"trace":"#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('select count(*)...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(414): Illuminate\\Database\\Connection->run('select count(*)...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2913): Illuminate\\Database\\Connection->select('select count(*)...', Array, true)
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2902): Illuminate\\Database\\Query\\Builder->runSelect()
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3456): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(2901): Illuminate\\Database\\Query\\Builder->onceWithColumns(Array, Object(Closure))
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3383): Illuminate\\Database\\Query\\Builder->get(Array)
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3311): Illuminate\\Database\\Query\\Builder->aggregate('count', Array)
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->count()
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->__call('count', Array)
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'count', Array)
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('count', Array)
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\app\\Services\\WorkflowService.php(302): Illuminate\\Database\\Eloquent\\Model::__callStatic('count', Array)
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\app\\Console\\Commands\\ProcessWorkflows.php(126): App\\Services\\WorkflowService->generateWorkflowReport()
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\app\\Console\\Commands\\ProcessWorkflows.php(64): App\\Console\\Commands\\ProcessWorkflows->generateReport()
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\ProcessWorkflows->handle()
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#21 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#22 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#23 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#24 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\ProcessWorkflows), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#25 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#26 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#27 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#28 {main}"} 
[2025-07-30 14:49:09] testing.INFO: Email envoyé à <EMAIL> pour notification 7  
[2025-07-30 14:49:09] testing.INFO: Email envoyé à <EMAIL> pour notification 8  
[2025-07-30 14:49:09] testing.ERROR: Erreur envoi notification 8 via sms: Utilisateur sans téléphone  
[2025-07-30 14:49:09] testing.INFO: Email envoyé à <EMAIL> pour notification 9  
[2025-07-30 14:52:23] local.WARNING: Failed login attempt {"email":"<EMAIL>","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-30 14:52:38] local.WARNING: Failed login attempt {"email":"<EMAIL>","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-30 14:54:28] local.WARNING: Failed login attempt {"email":"<EMAIL>","ip":"127.0.0.1","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"} 
[2025-07-30 14:54:56] local.ERROR: SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' (Connection: mysql, SQL: insert into `cotisations` (`numero_cotisation`, `adherent_id`, `periode_cotisation`, `annee_cotisation`, `mois_cotisation`, `montant_base`, `taux_cotisation`, `montant_cotisation`, `montant_employeur`, `montant_employe`, `date_echeance`, `statut`, `date_paiement`, `mode_paiement`, `reference_paiement`, `observations`, `created_by`, `uuid`, `updated_at`, `created_at`) values (COT202500103, 1, mars, 2025, 3, 1800.00, 0.1083, 195, 117, 78, 2025-03-31 23:59:59, payee, 2025-03-11 14:54:56, virement, REF689205, ?, 1, e708072f-b434-44aa-85bd-69e6a8ec7818, 2025-07-30 14:54:56, 2025-07-30 14:54:56)) {"exception":"[object] (Illuminate\\Database\\UniqueConstraintViolationException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' (Connection: mysql, SQL: insert into `cotisations` (`numero_cotisation`, `adherent_id`, `periode_cotisation`, `annee_cotisation`, `mois_cotisation`, `montant_base`, `taux_cotisation`, `montant_cotisation`, `montant_employeur`, `montant_employe`, `date_echeance`, `statut`, `date_paiement`, `mode_paiement`, `reference_paiement`, `observations`, `created_by`, `uuid`, `updated_at`, `created_at`) values (COT202500103, 1, mars, 2025, 3, 1800.00, 0.1083, 195, 117, 78, 2025-03-31 23:59:59, payee, 2025-03-11 14:54:56, virement, REF689205, ?, 1, e708072f-b434-44aa-85bd-69e6a8ec7818, 2025-07-30 14:54:56, 2025-07-30 14:54:56)) at C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:824)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `co...', Array, 'id')
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Modules\\Cotisations\\Models\\Cotisation))
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(Modules\\Cotisations\\Models\\Cotisation), Object(Closure))
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\CotisationSeeder.php(56): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\CotisationSeeder->run()
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#19 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#20 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#21 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#22 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#23 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#24 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#25 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#26 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#27 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#28 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#29 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#30 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#31 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#32 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#34 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#35 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#36 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#37 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#38 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#39 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#40 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#41 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#42 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(93): Illuminate\\Console\\Command->call('db:seed', Array)
#45 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(69): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#46 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#47 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#48 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#49 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#50 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#51 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#52 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#53 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#54 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#55 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#56 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 {main}

[previous exception] [object] (PDOException(code: 23000): SQLSTATE[23000]: Integrity constraint violation: 1062 Duplicate entry 'COT202500103' for key 'cotisations_numero_cotisation_unique' at C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php:45)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(45): PDOStatement->execute()
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(816): Illuminate\\Database\\MySqlConnection->Illuminate\\Database\\{closure}('insert into `co...', Array)
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(783): Illuminate\\Database\\Connection->runQueryCallback('insert into `co...', Array, Object(Closure))
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\MySqlConnection.php(34): Illuminate\\Database\\Connection->run('insert into `co...', Array, Object(Closure))
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Processors\\MySqlProcessor.php(35): Illuminate\\Database\\MySqlConnection->insert('insert into `co...', Array, 'id')
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3549): Illuminate\\Database\\Query\\Processors\\MySqlProcessor->processInsertGetId(Object(Illuminate\\Database\\Query\\Builder), 'insert into `co...', Array, 'id')
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1982): Illuminate\\Database\\Query\\Builder->insertGetId(Array, 'id')
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1334): Illuminate\\Database\\Eloquent\\Builder->__call('insertGetId', Array)
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1299): Illuminate\\Database\\Eloquent\\Model->insertAndSetId(Object(Illuminate\\Database\\Eloquent\\Builder), Array)
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1138): Illuminate\\Database\\Eloquent\\Model->performInsert(Object(Illuminate\\Database\\Eloquent\\Builder))
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1025): Illuminate\\Database\\Eloquent\\Model->save()
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Database\\Eloquent\\Builder->Illuminate\\Database\\Eloquent\\{closure}(Object(Modules\\Cotisations\\Models\\Cotisation))
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1024): tap(Object(Modules\\Cotisations\\Models\\Cotisation), Object(Closure))
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(23): Illuminate\\Database\\Eloquent\\Builder->create(Array)
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2335): Illuminate\\Database\\Eloquent\\Model->forwardCallTo(Object(Illuminate\\Database\\Eloquent\\Builder), 'create', Array)
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(2347): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\CotisationSeeder.php(56): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\CotisationSeeder->run()
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#19 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#20 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#21 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#22 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#23 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#24 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(61): Illuminate\\Database\\Seeder->__invoke(Array)
#25 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\database\\seeders\\DatabaseSeeder.php(15): Illuminate\\Database\\Seeder->call('Database\\\\Seeder...')
#26 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Database\\Seeders\\DatabaseSeeder->run()
#27 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#28 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#29 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#30 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#31 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(184): Illuminate\\Container\\Container->call(Array, Array)
#32 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Seeder.php(193): Illuminate\\Database\\Seeder->Illuminate\\Database\\{closure}()
#33 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Seeder->__invoke()
#34 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#35 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#36 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#37 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#38 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#39 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#40 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#41 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#42 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#43 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#44 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(67): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArrayInput), Object(Illuminate\\Console\\OutputStyle))
#45 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Concerns\\CallsCommands.php(28): Illuminate\\Console\\Command->runCommand('db:seed', Array, Object(Illuminate\\Console\\OutputStyle))
#46 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(93): Illuminate\\Console\\Command->call('db:seed', Array)
#47 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Migrations\\FreshCommand.php(69): Illuminate\\Database\\Console\\Migrations\\FreshCommand->runSeeder(NULL)
#48 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\FreshCommand->handle()
#49 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#50 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#51 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#52 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#53 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#54 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#55 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#56 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#57 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\FreshCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#58 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#59 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#60 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#61 {main}
"} 
[2025-07-30 14:59:34] local.ERROR: PHP Parse error: Syntax error, unexpected '=' on line 2 {"exception":"[object] (Psy\\Exception\\ParseErrorException(code: 0): PHP Parse error: Syntax error, unexpected '=' on line 2 at C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\Exception\\ParseErrorException.php:44)
[stacktrace]
#0 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\CodeCleaner.php(306): Psy\\Exception\\ParseErrorException::fromParseError(Object(PhpParser\\Error))
#1 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\CodeCleaner.php(240): Psy\\CodeCleaner->parse('<?php \\n = app(\\\\...', false)
#2 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\Shell.php(852): Psy\\CodeCleaner->clean(Array, false)
#3 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\Shell.php(881): Psy\\Shell->addCode('\\n = app(\\\\Module...', true)
#4 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\psy\\psysh\\src\\Shell.php(1390): Psy\\Shell->setCode('\\n = app(\\\\Module...', true)
#5 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\tinker\\src\\Console\\TinkerCommand.php(76): Psy\\Shell->execute('\\n = app(\\\\Module...')
#6 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Laravel\\Tinker\\Console\\TinkerCommand->handle()
#7 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#8 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#9 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#10 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#11 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#12 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#13 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#14 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Laravel\\Tinker\\Console\\TinkerCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 C:\\Users\\<USER>\\Documents\\ecarrier\\e-carriere\\crfm-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 {main}
"} 
