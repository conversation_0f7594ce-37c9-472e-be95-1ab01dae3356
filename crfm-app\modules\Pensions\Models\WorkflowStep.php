<?php

namespace Modules\Pensions\Models;

use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class WorkflowStep extends BaseModel
{
    /**
     * The attributes that are mass assignable.
     */
    protected $fillable = [
        'uuid',
        'dossier_pension_id',
        'step_name',
        'step_order',
        'statut',
        'date_debut',
        'date_fin',
        'assigned_to',
        'completed_by',
        'observations',
        'required_documents',
        'next_step_id'
    ];

    /**
     * The attributes that should be cast.
     */
    protected $casts = [
        'date_debut' => 'datetime',
        'date_fin' => 'datetime',
        'step_order' => 'integer',
        'required_documents' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Step constants
     */
    const STEP_RECEPTION = 'reception';
    const STEP_VERIFICATION_DOCUMENTS = 'verification_documents';
    const STEP_CALCUL_DROITS = 'calcul_droits';
    const STEP_VALIDATION_TECHNIQUE = 'validation_technique';
    const STEP_VALIDATION_HIERARCHIQUE = 'validation_hierarchique';
    const STEP_PRE_LIQUIDATION = 'pre_liquidation';
    const STEP_LIQUIDATION = 'liquidation';
    const STEP_MISE_EN_PAIEMENT = 'mise_en_paiement';

    /**
     * Status constants
     */
    const STATUS_EN_ATTENTE = 'en_attente';
    const STATUS_EN_COURS = 'en_cours';
    const STATUS_TERMINE = 'termine';
    const STATUS_BLOQUE = 'bloque';
    const STATUS_ANNULE = 'annule';

    /**
     * Get dossier pension that owns this workflow step
     */
    public function dossierPension(): BelongsTo
    {
        return $this->belongsTo(DossierPension::class);
    }

    /**
     * Get user assigned to this step
     */
    public function assignedUser(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'assigned_to');
    }

    /**
     * Get user who completed this step
     */
    public function completedByUser(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User::class, 'completed_by');
    }

    /**
     * Get next workflow step
     */
    public function nextStep(): BelongsTo
    {
        return $this->belongsTo(WorkflowStep::class, 'next_step_id');
    }

    /**
     * Get step name label
     */
    public function getStepLabelAttribute(): string
    {
        return match($this->step_name) {
            self::STEP_RECEPTION => 'Réception du dossier',
            self::STEP_VERIFICATION_DOCUMENTS => 'Vérification des documents',
            self::STEP_CALCUL_DROITS => 'Calcul des droits',
            self::STEP_VALIDATION_TECHNIQUE => 'Validation technique',
            self::STEP_VALIDATION_HIERARCHIQUE => 'Validation hiérarchique',
            self::STEP_PRE_LIQUIDATION => 'Pré-liquidation',
            self::STEP_LIQUIDATION => 'Liquidation',
            self::STEP_MISE_EN_PAIEMENT => 'Mise en paiement',
            default => 'Étape inconnue'
        };
    }

    /**
     * Get status label
     */
    public function getStatusLabelAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_ATTENTE => 'En attente',
            self::STATUS_EN_COURS => 'En cours',
            self::STATUS_TERMINE => 'Terminé',
            self::STATUS_BLOQUE => 'Bloqué',
            self::STATUS_ANNULE => 'Annulé',
            default => 'Statut inconnu'
        };
    }

    /**
     * Get status color for UI
     */
    public function getStatusColorAttribute(): string
    {
        return match($this->statut) {
            self::STATUS_EN_ATTENTE => 'secondary',
            self::STATUS_EN_COURS => 'warning',
            self::STATUS_TERMINE => 'success',
            self::STATUS_BLOQUE => 'danger',
            self::STATUS_ANNULE => 'dark',
            default => 'secondary'
        };
    }

    /**
     * Get duration in days
     */
    public function getDurationAttribute(): ?int
    {
        if ($this->date_debut && $this->date_fin) {
            return $this->date_debut->diffInDays($this->date_fin);
        }
        
        if ($this->date_debut && $this->statut === self::STATUS_EN_COURS) {
            return $this->date_debut->diffInDays(now());
        }

        return null;
    }

    /**
     * Check if step is completed
     */
    public function isCompleted(): bool
    {
        return $this->statut === self::STATUS_TERMINE;
    }

    /**
     * Check if step is in progress
     */
    public function isInProgress(): bool
    {
        return $this->statut === self::STATUS_EN_COURS;
    }

    /**
     * Check if step is blocked
     */
    public function isBlocked(): bool
    {
        return $this->statut === self::STATUS_BLOQUE;
    }

    /**
     * Scope for completed steps
     */
    public function scopeCompleted($query)
    {
        return $query->where('statut', self::STATUS_TERMINE);
    }

    /**
     * Scope for in progress steps
     */
    public function scopeInProgress($query)
    {
        return $query->where('statut', self::STATUS_EN_COURS);
    }

    /**
     * Scope for pending steps
     */
    public function scopePending($query)
    {
        return $query->where('statut', self::STATUS_EN_ATTENTE);
    }

    /**
     * Start the step
     */
    public function start(int $assignedTo = null): void
    {
        $this->update([
            'statut' => self::STATUS_EN_COURS,
            'date_debut' => now(),
            'assigned_to' => $assignedTo ?? $this->assigned_to,
        ]);
    }

    /**
     * Complete the step
     */
    public function complete(int $completedBy, string $observations = null): void
    {
        $this->update([
            'statut' => self::STATUS_TERMINE,
            'date_fin' => now(),
            'completed_by' => $completedBy,
            'observations' => $observations,
        ]);
    }

    /**
     * Block the step
     */
    public function block(string $reason): void
    {
        $this->update([
            'statut' => self::STATUS_BLOQUE,
            'observations' => $reason,
        ]);
    }

    /**
     * Get default workflow steps for pension type
     */
    public static function getDefaultWorkflowSteps(string $pensionType): array
    {
        $baseSteps = [
            ['name' => self::STEP_RECEPTION, 'order' => 1],
            ['name' => self::STEP_VERIFICATION_DOCUMENTS, 'order' => 2],
            ['name' => self::STEP_CALCUL_DROITS, 'order' => 3],
            ['name' => self::STEP_VALIDATION_TECHNIQUE, 'order' => 4],
            ['name' => self::STEP_VALIDATION_HIERARCHIQUE, 'order' => 5],
            ['name' => self::STEP_PRE_LIQUIDATION, 'order' => 6],
            ['name' => self::STEP_LIQUIDATION, 'order' => 7],
            ['name' => self::STEP_MISE_EN_PAIEMENT, 'order' => 8],
        ];

        // Additional steps for specific pension types could be added here
        return $baseSteps;
    }
}
