<!DOCTYPE html>
<html lang="fr">
<head>
    <title>CRFM - Tableau de bord</title>
    <!-- Meta -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=0, minimal-ui">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="description" content="Application de gestion des cotisations et de pré-liquidation de pension pour le CRFM">
    <meta name="keywords" content="CRFM, cotisations, pension, pré-liquidation, organisme public">
    <meta name="author" content="CRFM">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <!-- Favicon icon -->
    <link rel="icon" href="{{ asset('files/assets/images/favicon.ico') }}" type="image/x-icon">
    <!-- Google font-->
    <link href="https://fonts.googleapis.com/css?family=Open+Sans:400,600" rel="stylesheet">
    <!-- Required Fremwork -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/bower_components/bootstrap/dist/css/bootstrap.min.css') }}">
    <!-- feather Awesome -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/icon/feather/css/feather.css') }}">
    <!-- Font Awesome -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/icon/font-awesome/css/font-awesome.min.css') }}">
    <!-- Chart.js -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/chartist@0.11.4/dist/chartist.min.css" type="text/css">
    <!-- Style.css -->
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/css/style.css') }}">
    <link rel="stylesheet" type="text/css" href="{{ asset('files/assets/css/jquery.mCustomScrollbar.css') }}">
    <style>
        .welcome-card {
            background: linear-gradient(to right, #1a237e, #283593);
            color: white;
            border-radius: 5px;
        }
        .welcome-card h2, .welcome-card p {
            color: white;
        }
        .task-list-card .task-item {
            padding: 10px 0;
            border-bottom: 1px solid #f1f1f1;
        }
        .task-list-card .task-item:last-child {
            border-bottom: none;
        }
        .task-priority {
            width: 12px;
            height: 12px;
            display: inline-block;
            border-radius: 50%;
            margin-right: 5px;
        }
        .priority-high {
            background-color: #f44336;
        }
        .priority-medium {
            background-color: #ff9800;
        }
        .priority-low {
            background-color: #4caf50;
        }
        .dashboard-header {
            margin-bottom: 20px;
        }
        .quick-stats {
            margin-bottom: 20px;
        }
        .chart-container {
            position: relative;
            height: 300px;
            width: 100%;
            margin-bottom: 15px;
        }
        .chart-filters {
            position: relative;
            z-index: 10;
            margin-bottom: 10px;
        }

        /* Styles pour les cartes colorées du dashboard */
        .bg-c-blue {
            background: linear-gradient(45deg, #4099ff, #73b4ff) !important;
            color: white !important;
        }
        .bg-c-green {
            background: linear-gradient(45deg, #2ed8b6, #59e0c5) !important;
            color: white !important;
        }
        .bg-c-yellow {
            background: linear-gradient(45deg, #FFB64D, #ffcb80) !important;
            color: white !important;
        }
        .bg-c-red {
            background: linear-gradient(45deg, #FF5370, #ff869a) !important;
            color: white !important;
        }
        .bg-c-orange {
            background: linear-gradient(45deg, #FF8A65, #ffab91) !important;
            color: white !important;
        }
        .bg-c-purple {
            background: linear-gradient(45deg, #9C27B0, #ba68c8) !important;
            color: white !important;
        }
        .bg-c-pink {
            background: linear-gradient(45deg, #E91E63, #f06292) !important;
            color: white !important;
        }

        /* Forcer le texte blanc sur les cartes colorées */
        .bg-c-blue *, .bg-c-green *, .bg-c-yellow *, .bg-c-red *,
        .bg-c-orange *, .bg-c-purple *, .bg-c-pink * {
            color: white !important;
        }

        /* Styles pour les icônes */
        .f-28 {
            font-size: 28px !important;
        }
        .f-w-600 {
            font-weight: 600 !important;
        }
        .m-b-0 {
            margin-bottom: 0 !important;
        }

        /* Assurer la visibilité du texte */
        .text-white {
            color: white !important;
        }

        /* Style pour les cartes avec bordure arrondie */
        .card {
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border: none;
        }

        .card-block {
            padding: 20px;
        }
    </style>
</head>


<body>
    <!-- Pre-loader start -->
    <div class="theme-loader">
        <div class="ball-scale">
            <div class='contain'>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
                <div class="ring"><div class="frame"></div></div>
            </div>
        </div>
    </div>
    <!-- Pre-loader end -->

    <div id="pcoded" class="pcoded">
        <div class="pcoded-overlay-box"></div>
        <div class="pcoded-container navbar-wrapper">
            <nav class="navbar header-navbar pcoded-header">
                <div class="navbar-wrapper">
                    <div class="navbar-logo">
                        <a class="mobile-menu" id="mobile-collapse" href="#!">
                            <i class="feather icon-menu"></i>
                        </a>
                        <a href="{{ route('dashboard.index') }}">
                            <h4 class="text-center py-2 m-0">CRFM</h4>
                        </a>
                        <a class="mobile-options">
                            <i class="feather icon-more-horizontal"></i>
                        </a>
                    </div>
                    <div class="navbar-container container-fluid">
                        <ul class="nav-right">
                            <li class="user-profile header-notification">
                                <div class="dropdown-primary dropdown">
                                    <div class="dropdown-toggle" data-bs-toggle="dropdown">
                                        <img src="{{ asset('files/assets/images/avatar-4.jpg') }}" class="img-radius" alt="User-Profile-Image">
                                        <span>{{ auth()->user()->name }}</span>
                                        <i class="feather icon-chevron-down"></i>
                                    </div>
                                    <ul class="show-notification profile-notification dropdown-menu" data-dropdown-in="fadeIn" data-dropdown-out="fadeOut">
                                        <li>
                                            <a href="#!">
                                                <i class="feather icon-settings"></i> Paramètres
                                            </a>
                                        </li>
                                        <li>
                                            <a href="#!">
                                                <i class="feather icon-user"></i> Profil
                                            </a>
                                        </li>
                                        <li>
                                            <form method="POST" action="{{ route('auth.logout') }}">
                                                @csrf
                                                <button type="submit" style="background: none; border: none; width: 100%; text-align: left; padding: 10px 20px; color: inherit;">
                                                    <i class="feather icon-log-out"></i> Quitter
                                                </button>
                                            </form>
                                        </li>
                                    </ul>
                                </div>
                            </li>
                        </ul>
                    </div>
                </div>
            </nav>

            <!-- Menu sidebar -->
            <div class="pcoded-main-container">
                <div class="pcoded-wrapper">
                    <nav class="pcoded-navbar">
                        <div class="pcoded-inner-navbar main-menu">
                            <div class="pcoded-navigatio-lavel">Menu principal</div>
                            <ul class="pcoded-item pcoded-left-item">
                                <li class="active">
                                    <a href="{{ route('dashboard.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-home"></i></span>
                                        <span class="pcoded-mtext">Tableau de bord</span>
                                    </a>
                                </li>
                                <li class="pcoded-hasmenu">
                                    <a href="javascript:void(0)">
                                        <span class="pcoded-micon"><i class="feather icon-users"></i></span>
                                        <span class="pcoded-mtext">Dossiers</span>
                                    </a>
                                    <ul class="pcoded-submenu">
                                        <li class="">
                                            <a href="{{ route('affiliation.index') }}">
                                                <span class="pcoded-mtext">Affiliation</span>
                                            </a>
                                        </li>
                                        <li class="">
                                            <a href="{{ route('declaration.index') }}">
                                                <span class="pcoded-mtext">Déclaration</span>
                                            </a>
                                        </li>
                                        <li class="">
                                            <a href="{{ route('pre-liquidation.index') }}">
                                                <span class="pcoded-mtext">Pré-liquidation</span>
                                            </a>
                                        </li>
                                    </ul>
                                </li>
                                <li class="">
                                    <a href="{{ route('statistiques.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-bar-chart"></i></span>
                                        <span class="pcoded-mtext">Statistiques</span>
                                    </a>
                                </li>
                                <li class="">
                                    <a href="{{ route('parametres.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-settings"></i></span>
                                        <span class="pcoded-mtext">Paramètres</span>
                                    </a>
                                </li>
                                <li class="">
                                    <a href="{{ route('outils.index') }}">
                                        <span class="pcoded-micon"><i class="feather icon-tool"></i></span>
                                        <span class="pcoded-mtext">Outils</span>
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </nav>
                    <div class="pcoded-content">
                        <div class="pcoded-inner-content">
                            <div class="main-body">
                                <div class="page-wrapper">
                                    <div class="page-body">
                                        <!-- Dashboard Header -->
                                        <div class="row dashboard-header">
                                            <div class="col-md-12">
                                                <div class="card welcome-card">
                                                    <div class="card-block">
                                                        <div class="row">
                                                            <div class="col-md-8">
                                                                <h2>Bienvenue sur le portail CRFM</h2>
                                                                <p class="mb-0">Tableau de bord de gestion des cotisations et pré-liquidations de pension</p>
                                                                <p>Date du jour : <span id="current-date">{{ now()->format('d/m/Y') }}</span></p>
                                                            </div>
                                                            <div class="col-md-4 text-end d-flex align-items-center justify-content-end">
                                                                <button class="btn btn-light me-2" onclick="location.reload()"><i class="feather icon-refresh-cw"></i> Actualiser</button>
                                                                <button class="btn btn-light" onclick="window.print()"><i class="feather icon-printer"></i> Imprimer</button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Quick Stats -->
                                        <div class="row quick-stats">
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-blue text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['adherents_actifs']) ? number_format($stats['adherents_actifs']) : '0' }}</h4>
                                                                <h6 class="text-white m-b-0">Adhérents actifs</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-users f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-green text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">3,452</h4>
                                                                <h6 class="text-white m-b-0">Retraités</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-award f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-yellow text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['cotisations_mois']) ? number_format($stats['cotisations_mois']) . ' €' : '0 €' }}</h4>
                                                                <h6 class="text-white m-b-0">Cotisations ce mois</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-bar-chart f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-red text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['cotisations_en_retard']) ? number_format($stats['cotisations_en_retard']) : '0' }}</h4>
                                                                <h6 class="text-white m-b-0">Cotisations en retard</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-alert-triangle f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Statistiques supplémentaires -->
                                        <div class="row">
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-green text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['total_adherents']) ? number_format($stats['total_adherents']) : '0' }}</h4>
                                                                <h6 class="text-white m-b-0">Total adhérents</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-users f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-orange text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['adherents_suspendus']) ? number_format($stats['adherents_suspendus']) : '0' }}</h4>
                                                                <h6 class="text-white m-b-0">Adhérents suspendus</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-user-x f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-purple text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['montant_total_cotisations']) ? number_format($stats['montant_total_cotisations']) . ' €' : '0 €' }}</h4>
                                                                <h6 class="text-white m-b-0">Total cotisations payées</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-dollar-sign f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-3 col-md-6">
                                                <div class="card bg-c-pink text-white">
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-white f-w-600">{{ isset($stats['pensions_liquidees']) ? number_format($stats['pensions_liquidees']) : '0' }}</h4>
                                                                <h6 class="text-white m-b-0">Pensions liquidées</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-check-circle f-28"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Actions rapides -->
                                        <div class="row">
                                            <div class="col-md-12">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><i class="feather icon-zap text-c-red me-2"></i>Actions rapides</h5>
                                                    </div>
                                                    <div class="card-block">
                                                        <div class="row">
                                                            <div class="col-md-3">
                                                                <a href="{{ route('cotisations.create') }}" class="btn btn-primary btn-block">
                                                                    <i class="feather icon-plus me-2"></i>Nouvelle Cotisation
                                                                </a>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <a href="{{ route('pensions.create') }}" class="btn btn-success btn-block">
                                                                    <i class="feather icon-file-text me-2"></i>Dossier Pension
                                                                </a>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <button class="btn btn-info btn-block" onclick="sendNotification()">
                                                                    <i class="feather icon-mail me-2"></i>Envoyer Notification
                                                                </button>
                                                            </div>
                                                            <div class="col-md-3">
                                                                <button class="btn btn-warning btn-block" onclick="exportData()">
                                                                    <i class="feather icon-download me-2"></i>Exporter Données
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Blocs d'événements -->
                                        <div class="row">
                                            <!-- Blocs d'événements -->
                                            <div class="col-xl-4 col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><i class="feather icon-user-plus text-c-blue me-2"></i> Affiliation</h5>
                                                    </div>
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-c-blue f-w-600">24</h4>
                                                                <h6 class="text-muted m-b-0">Nouvelles affiliations</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-user-plus f-28 text-c-blue"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer bg-c-blue">
                                                        <div class="row align-items-center">
                                                            <div class="col-9">
                                                                <p class="text-white m-b-0">En attente de validation</p>
                                                            </div>
                                                            <div class="col-3 text-end">
                                                                <a href="{{ route('affiliation.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><i class="feather icon-file-text text-c-green me-2"></i> Déclarations</h5>
                                                    </div>
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-c-green f-w-600">156</h4>
                                                                <h6 class="text-muted m-b-0">Déclarations reçues</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-file-text f-28 text-c-green"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer bg-c-green">
                                                        <div class="row align-items-center">
                                                            <div class="col-9">
                                                                <p class="text-white m-b-0">À traiter ce mois</p>
                                                            </div>
                                                            <div class="col-3 text-end">
                                                                <a href="{{ route('declaration.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-xl-4 col-md-6">
                                                <div class="card">
                                                    <div class="card-header">
                                                        <h5><i class="feather icon-briefcase text-c-yellow me-2"></i> Pré-liquidation</h5>
                                                    </div>
                                                    <div class="card-block">
                                                        <div class="row align-items-center">
                                                            <div class="col-8">
                                                                <h4 class="text-c-yellow f-w-600">{{ isset($stats['pensions_en_cours']) ? $stats['pensions_en_cours'] : '42' }}</h4>
                                                                <h6 class="text-muted m-b-0">Dossiers en cours</h6>
                                                            </div>
                                                            <div class="col-4 text-end">
                                                                <i class="feather icon-briefcase f-28 text-c-yellow"></i>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div class="card-footer bg-c-yellow">
                                                        <div class="row align-items-center">
                                                            <div class="col-9">
                                                                <p class="text-white m-b-0">Prêts pour liquidation</p>
                                                            </div>
                                                            <div class="col-3 text-end">
                                                                <a href="{{ route('pre-liquidation.index') }}" class="text-white"><i class="feather icon-chevrons-right m-l-5"></i></a>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Required Js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery/dist/jquery.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery-ui/jquery-ui.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/popper.js/dist/umd/popper.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/bootstrap/dist/js/bootstrap.min.js') }}"></script>
    <!-- jquery slimscroll js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/jquery-slimscroll/jquery.slimscroll.js') }}"></script>
    <!-- modernizr js -->
    <script type="text/javascript" src="{{ asset('files/bower_components/modernizr/modernizr.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/bower_components/modernizr/feature-detects/css-scrollbars.js') }}"></script>
    <!-- Custom js -->
    <script src="{{ asset('files/assets/js/pcoded.min.js') }}"></script>
    <script src="{{ asset('files/assets/js/vartical-layout.min.js') }}"></script>
    <script src="{{ asset('files/assets/js/jquery.mCustomScrollbar.concat.min.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/assets/js/script.js') }}"></script>
    <script type="text/javascript" src="{{ asset('files/assets/js/crfm-custom.js') }}"></script>

    <!-- Scripts pour les actions rapides -->
    <script>
    function sendNotification() {
        // Modal pour choisir le type de notification
        const notificationTypes = [
            { value: 'cotisation_rappel', text: 'Rappel de cotisation' },
            { value: 'pension_info', text: 'Information pension' },
            { value: 'document_manquant', text: 'Document manquant' },
            { value: 'general', text: 'Notification générale' }
        ];

        let options = notificationTypes.map(type =>
            `<option value="${type.value}">${type.text}</option>`
        ).join('');

        const modalHtml = `
            <div class="modal fade" id="notificationModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Envoyer une notification</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="notificationForm">
                                <div class="form-group">
                                    <label>Type de notification</label>
                                    <select class="form-control" name="type" required>
                                        <option value="">Sélectionner</option>
                                        ${options}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Destinataires</label>
                                    <select class="form-control" name="recipients" required>
                                        <option value="">Sélectionner</option>
                                        <option value="all_adherents">Tous les adhérents</option>
                                        <option value="active_adherents">Adhérents actifs</option>
                                        <option value="pending_cotisations">Cotisations en retard</option>
                                        <option value="pension_eligible">Éligibles pension</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Message (optionnel)</label>
                                    <textarea class="form-control" name="message" rows="3"
                                              placeholder="Message personnalisé..."></textarea>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-info" onclick="confirmSendNotification()">Envoyer</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprimer le modal existant s'il y en a un
        $('#notificationModal').remove();

        // Ajouter le modal au DOM
        $('body').append(modalHtml);

        // Afficher le modal
        $('#notificationModal').modal('show');
    }

    function confirmSendNotification() {
        const form = document.getElementById('notificationForm');
        const formData = new FormData(form);

        if (!formData.get('type') || !formData.get('recipients')) {
            alert('Veuillez remplir tous les champs obligatoires');
            return;
        }

        // Simulation d'envoi
        $('#notificationModal').modal('hide');

        // Afficher un message de succès
        setTimeout(() => {
            alert('Notification envoyée avec succès !');
        }, 500);

        // TODO: Implémenter l'envoi réel via AJAX
        // fetch('/api/notifications/send', { method: 'POST', body: formData })
    }

    function exportData() {
        // Modal pour choisir le type d'export
        const exportTypes = [
            { value: 'adherents', text: 'Liste des adhérents', url: '/adherents/export' },
            { value: 'cotisations', text: 'Cotisations', url: '/cotisations/export' },
            { value: 'pensions', text: 'Dossiers de pension', url: '/pensions/export' },
            { value: 'statistiques', text: 'Rapport statistiques', url: '/reports/statistics' }
        ];

        let options = exportTypes.map(type =>
            `<option value="${type.value}" data-url="${type.url}">${type.text}</option>`
        ).join('');

        const modalHtml = `
            <div class="modal fade" id="exportModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Exporter des données</h5>
                            <button type="button" class="close" data-dismiss="modal">
                                <span>&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">
                            <form id="exportForm">
                                <div class="form-group">
                                    <label>Type de données</label>
                                    <select class="form-control" name="type" required>
                                        <option value="">Sélectionner</option>
                                        ${options}
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Format</label>
                                    <select class="form-control" name="format" required>
                                        <option value="">Sélectionner</option>
                                        <option value="excel">Excel (.xlsx)</option>
                                        <option value="csv">CSV</option>
                                        <option value="pdf">PDF</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label>Période (optionnel)</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="date" class="form-control" name="date_debut" placeholder="Date début">
                                        </div>
                                        <div class="col-6">
                                            <input type="date" class="form-control" name="date_fin" placeholder="Date fin">
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-dismiss="modal">Annuler</button>
                            <button type="button" class="btn btn-warning" onclick="confirmExportData()">Exporter</button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Supprimer le modal existant s'il y en a un
        $('#exportModal').remove();

        // Ajouter le modal au DOM
        $('body').append(modalHtml);

        // Afficher le modal
        $('#exportModal').modal('show');
    }

    function confirmExportData() {
        const form = document.getElementById('exportForm');
        const formData = new FormData(form);

        if (!formData.get('type') || !formData.get('format')) {
            alert('Veuillez remplir tous les champs obligatoires');
            return;
        }

        // Récupérer l'URL d'export
        const typeSelect = form.querySelector('select[name="type"]');
        const selectedOption = typeSelect.options[typeSelect.selectedIndex];
        const baseUrl = selectedOption.getAttribute('data-url');

        if (!baseUrl) {
            alert('URL d\'export non trouvée');
            return;
        }

        // Construire l'URL avec les paramètres
        const params = new URLSearchParams();
        params.append('format', formData.get('format'));
        if (formData.get('date_debut')) params.append('date_debut', formData.get('date_debut'));
        if (formData.get('date_fin')) params.append('date_fin', formData.get('date_fin'));

        const exportUrl = `${baseUrl}?${params.toString()}`;

        // Fermer le modal
        $('#exportModal').modal('hide');

        // Ouvrir l'export dans un nouvel onglet
        window.open(exportUrl, '_blank');
    }
    </script>
</body>
</html>
